"""Custom exceptions for LLM service."""

from typing import Optional, Dict, Any


class LLMServiceException(Exception):
    """Base exception for LLM service."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}


class LLMProviderException(LLMServiceException):
    """Exception raised when LLM provider fails."""


class ValidationException(LLMServiceException):
    """Exception raised when data validation fails."""


class MemoryServiceException(LLMServiceException):
    """Exception raised when memory service operations fail."""


class ConfigurationException(LLMServiceException):
    """Exception raised when configuration is invalid."""


class LLMProcessingError(LLMProviderException):
    """Exception raised when LLM processing fails."""


class ConfigurationError(ConfigurationException):
    """Exception raised when configuration is invalid."""