#!/usr/bin/env python3
"""
MYOB EXO API Comprehensive Endpoint Builder
Creates comprehensive endpoints file based on scraped sidebar data from developer.myob.com
"""

import json
from datetime import datetime
from typing import Dict, List, Any

def build_comprehensive_endpoints():
    """Build comprehensive endpoints from scraped sidebar data"""
    
    # Base URI pattern from the documentation
    base_uri = "{URI}"  # This gets replaced with actual server URL like http://***********:8888
    
    # All endpoints extracted from the sidebar navigation
    sidebar_endpoints = [
        {"title": "Endpoints", "href": "/api/exo/endpoints/", "category": "discovery"},
        
        # Debtor/Customer Endpoints
        {"title": "Debtor", "href": "/api/exo/endpoints/debtor/", "category": "debtors"},
        {"title": "Debtor History Note", "href": "/api/exo/endpoints/debtor/debtorhistorynote/", "category": "debtors"},
        {"title": "Debtor Transaction", "href": "/api/exo/endpoints/debtor/debtortrans/", "category": "debtors"},
        {"title": "Debtor Transaction Report", "href": "/api/exo/endpoints/debtor/debtorreport/", "category": "debtors"},
        {"title": "Debtor Statement", "href": "/api/exo/endpoints/debtor/debtorstatement/", "category": "debtors"},
        {"title": "Debtor Activities", "href": "/api/exo/endpoints/debtor/debtoractivity/", "category": "debtors"},
        
        # Prospect Endpoints
        {"title": "Prospect", "href": "/api/exo/endpoints/prospect/", "category": "prospects"},
        {"title": "Prospect History Note", "href": "/api/exo/endpoints/prospect/prospecthistorynote/", "category": "prospects"},
        {"title": "Prospect Activities", "href": "/api/exo/endpoints/prospect/prospectactivity/", "category": "prospects"},
        
        # Stock Management Endpoints
        {"title": "Stock", "href": "/api/exo/endpoints/stock/", "category": "stock_management"},
        {"title": "Stock Price Group", "href": "/api/exo/endpoints/stock/stockpricegroup/", "category": "stock_management"},
        {"title": "Stock Unit of Measure", "href": "/api/exo/endpoints/stock/stockunitofmeasure/", "category": "stock_management"},
        {"title": "Stock Location", "href": "/api/exo/endpoints/stock/stocklocation/", "category": "stock_management"},
        {"title": "Stock Classification", "href": "/api/exo/endpoints/stock/stockclassification/", "category": "stock_management"},
        {"title": "Secondary Stock Group", "href": "/api/exo/endpoints/stock/stocksecondarygroup/", "category": "stock_management"},
        {"title": "Primary Stock Group", "href": "/api/exo/endpoints/stock/stockprimarygroup/", "category": "stock_management"},
        {"title": "Stock Item Report", "href": "/api/exo/endpoints/stock/stockitemreport/", "category": "stock_management"},
        {"title": "Stock Item", "href": "/api/exo/endpoints/stock_item/", "category": "stock_management"},
        {"title": "Stock Item Price", "href": "/api/exo/endpoints/stock_item/stock_bestprice/", "category": "stock_management"},
        
        # Sales Order Endpoints
        {"title": "Sales Order", "href": "/api/exo/endpoints/salesorder/", "category": "sales_orders"},
        {"title": "Sales Order Report", "href": "/api/exo/endpoints/salesorder/salesorderreport/", "category": "sales_orders"},
        {"title": "Bill Of Materials", "href": "/api/exo/endpoints/salesorder/bom/", "category": "sales_orders"},
        {"title": "Bill of Materials Price", "href": "/api/exo/endpoints/salesorder/bom/bom_bestprice/", "category": "sales_orders"},
        
        # Report Endpoints
        {"title": "Report", "href": "/api/exo/endpoints/report/", "category": "reports"},
        {"title": "Report Parameters", "href": "/api/exo/endpoints/report/reportparameters/", "category": "reports"},
        {"title": "Run Report", "href": "/api/exo/endpoints/report/runreport/", "category": "reports"},
        {"title": "Fetch Report", "href": "/api/exo/endpoints/report/fetchreport/", "category": "reports"},
        
        # Job Management Endpoints
        {"title": "Job Project", "href": "/api/exo/endpoints/jobproject/", "category": "job_management"},
        {"title": "Job Type", "href": "/api/exo/endpoints/jobtype/", "category": "job_management"},
        {"title": "Job Status", "href": "/api/exo/endpoints/jobstatus/", "category": "job_management"},
        {"title": "Job Flag", "href": "/api/exo/endpoints/jobflagdescription/", "category": "job_management"},
        {"title": "Job Category", "href": "/api/exo/endpoints/jobcategory/", "category": "job_management"},
        
        # Contact and Company Endpoints
        {"title": "Contact", "href": "/api/exo/endpoints/contact/", "category": "contacts"},
        
        # Search Template Endpoints
        {"title": "Search Template", "href": "/api/exo/endpoints/searchtemplate/", "category": "search_templates"},
        {"title": "Stock Search Template", "href": "/api/exo/endpoints/stocksearchtemplate/", "category": "search_templates"},
        {"title": "Company Search Template", "href": "/api/exo/endpoints/companysearchtemplate/", "category": "search_templates"},
        {"title": "Geolocation Search Template", "href": "/api/exo/endpoints/geosearchtemplate/", "category": "search_templates"},
        
        # System/Authentication Endpoints
        {"title": "Token", "href": "/api/exo/endpoints/token/", "category": "system"},
        {"title": "Company Data File Info", "href": "/api/exo/endpoints/companydatafileinfo/", "category": "system"},
        
        # Activity Endpoints
        {"title": "Activity", "href": "/api/exo/endpoints/activity/", "category": "activities"},
        {"title": "Activity Type", "href": "/api/exo/endpoints/activitytype/", "category": "activities"},
        {"title": "Activity Status", "href": "/api/exo/endpoints/activitystatus/", "category": "activities"},
        
        # Custom Table Endpoints
        {"title": "Custom Table", "href": "/api/exo/endpoints/custom-table/", "category": "system"},
        
        # Purchase Order Endpoints (KEY for requirements)
        {"title": "Purchase Order", "href": "/api/exo/endpoints/purchaseorder/", "category": "purchase_orders"},
        
        # List Endpoints (Reference Data)
        {"title": "List Endpoints", "href": "/api/exo/endpoints/list-endpoints/", "category": "reference_data"},
        {"title": "Tax Rate", "href": "/api/exo/endpoints/list-endpoints/taxrate/", "category": "reference_data"},
        {"title": "Staff", "href": "/api/exo/endpoints/list-endpoints/staff/", "category": "reference_data"},
        {"title": "Payment Type", "href": "/api/exo/endpoints/list-endpoints/paymenttype/", "category": "reference_data"},
        {"title": "Currency", "href": "/api/exo/endpoints/list-endpoints/currency/", "category": "reference_data"},
        {"title": "Credit Term", "href": "/api/exo/endpoints/list-endpoints/creditterm/", "category": "reference_data"},
        {"title": "Company", "href": "/api/exo/endpoints/list-endpoints/company/", "category": "reference_data"},
        {"title": "Communication Process", "href": "/api/exo/endpoints/list-endpoints/communicationprocess/", "category": "reference_data"},
        {"title": "Common Phrases", "href": "/api/exo/endpoints/list-endpoints/commonphrases/", "category": "reference_data"},
        {"title": "Branch", "href": "/api/exo/endpoints/list-endpoints/branch/", "category": "reference_data"},
        {"title": "Base Price", "href": "/api/exo/endpoints/list-endpoints/baseprice/", "category": "reference_data"},
        {"title": "Advert Type", "href": "/api/exo/endpoints/list-endpoints/adverttype/", "category": "reference_data"},
        {"title": "Account Group", "href": "/api/exo/endpoints/list-endpoints/accountgroup/", "category": "reference_data"},
        
        # Creditor Endpoints (KEY for requirements)
        {"title": "Creditor", "href": "/api/exo/endpoints/creditor/", "category": "creditors"},
        {"title": "Creditor History Note", "href": "/api/exo/endpoints/creditor/creditorhistorynote/", "category": "creditors"},
    ]
    
    # Convert to full endpoint structure with additional metadata
    endpoints = []
    
    for ep in sidebar_endpoints:
        # Convert documentation URL to API endpoint URL
        api_path = ep["href"].replace("/api/exo/endpoints/", "/").replace("/", "")
        if not api_path:
            api_path = "discovery"
        
        # Build the full endpoint structure
        endpoint = {
            "rel": f"collection/{api_path}",
            "title": ep["title"],
            "href": f"{base_uri}/{api_path}",
            "methods": ["GET"],  # Default, most EXO endpoints support GET
            "category": ep["category"],
            "description": f"{ep['title']} management and operations",
            "documentation_url": f"https://developer.myob.com{ep['href']}"
        }
        
        # Add specific enhancements for key endpoints
        if "search" in ep["title"].lower() or ep["title"] in ["Debtor", "Creditor", "Stock", "Sales Order", "Purchase Order"]:
            endpoint["search_supported"] = True
            endpoint["search_href"] = f"{base_uri}/{api_path}/search?q={{query}}"
        
        # Add report endpoints for main entities
        if ep["title"] in ["Stock Item", "Sales Order", "Purchase Order", "Debtor", "Creditor"]:
            endpoint["report_href"] = f"{base_uri}/{api_path}/{{id}}/report"
        
        # Add POST support for transaction endpoints
        if any(word in ep["title"].lower() for word in ["transaction", "order", "activity"]):
            endpoint["methods"].append("POST")
        
        endpoints.append(endpoint)
    
    # Add the stock operations endpoints from the existing endpoints.json
    stock_operations = [
        {
            "rel": "collection/stockitem",
            "title": "StockItem",
            "href": f"{base_uri}/stockitem",
            "methods": ["GET"],
            "category": "stock_management",
            "description": "Stock item management",
            "search_supported": True,
            "search_href": f"{base_uri}/stockitem/search"
        },
        {
            "rel": "collection/stock/sale",
            "title": "Stock Sale",
            "href": f"{base_uri}/stock/sale",
            "methods": ["GET", "POST"],
            "category": "stock_operations",
            "description": "Stock sale transactions"
        },
        {
            "rel": "collection/stock/receipt",
            "title": "Stock Receipt",
            "href": f"{base_uri}/stock/receipt",
            "methods": ["GET", "POST"],
            "category": "stock_operations",
            "description": "Stock receipt transactions"
        },
        {
            "rel": "collection/stock/transfer",
            "title": "Stock Transfer",
            "href": f"{base_uri}/stock/transfer",
            "methods": ["GET", "POST"],
            "category": "stock_operations",
            "description": "Stock transfer between locations"
        },
        {
            "rel": "collection/stock/adjustin",
            "title": "Stock AdjustIn",
            "href": f"{base_uri}/stock/adjustin",
            "methods": ["GET", "POST"],
            "category": "stock_operations",
            "description": "Stock adjustment inward"
        },
        {
            "rel": "collection/stock/adjustout",
            "title": "Stock AdjustOut",
            "href": f"{base_uri}/stock/adjustout",
            "methods": ["GET", "POST"],
            "category": "stock_operations",
            "description": "Stock adjustment outward"
        },
        {
            "rel": "collection/stock/transaction",
            "title": "Stock Transactions",
            "href": f"{base_uri}/stock/transaction/{{ledger_type}}/{{id}}",
            "methods": ["GET"],
            "category": "stock_operations",
            "description": "Stock transaction history"
        }
    ]
    
    endpoints.extend(stock_operations)
    
    # Add the schema endpoint mentioned in requirements
    schema_endpoint = {
        "rel": "collection/schema",
        "title": "GetForm Schema",
        "href": f"{base_uri}/schema/getform",
        "methods": ["GET"],
        "category": "schemas",
        "description": "Form schema definitions for API validation and form generation"
    }
    endpoints.append(schema_endpoint)
    
    # Create the final structure
    endpoints_data = {
        "metadata": {
            "scraped_date": datetime.now().isoformat(),
            "source": "https://developer.myob.com/api/exo/endpoints/",
            "total_endpoints": len(endpoints),
            "categories": sorted(list(set([ep["category"] for ep in endpoints]))),
            "base_uri_pattern": base_uri,
            "notes": [
                "Replace {URI} with your actual MYOB EXO API server address",
                "e.g., http://***********:8888 for on-premise or exo.api.myob.com for cloud",
                "All endpoints support GET by default, additional methods listed where applicable",
                "Search endpoints require query parameter: ?q={search_term}",
                "Report endpoints require ID parameter in the URL path"
            ]
        },
        "endpoints": endpoints
    }
    
    return endpoints_data

def categorize_endpoints():
    """Create categorized endpoint structure optimized for the analyst agent"""
    
    endpoints_data = build_comprehensive_endpoints()
    
    categorized = {
        "purchase_orders": [],
        "creditors": [],
        "stock_management": [],
        "stock_operations": [],
        "sales_orders": [],
        "debtors": [],
        "reports": [],
        "reference_data": [],
        "search_templates": [],
        "activities": [],
        "job_management": [],
        "prospects": [],
        "contacts": [],
        "system": [],
        "schemas": [],
        "discovery": []
    }
    
    for endpoint in endpoints_data["endpoints"]:
        category = endpoint.get("category", "unknown")
        if category in categorized:
            categorized[category].append(endpoint)
        else:
            # Handle any uncategorized endpoints
            if "unknown" not in categorized:
                categorized["unknown"] = []
            categorized["unknown"].append(endpoint)
    
    return {
        "metadata": endpoints_data["metadata"],
        "categorized_endpoints": categorized,
        "all_endpoints": endpoints_data["endpoints"],
        "analyst_focus": {
            "chinese_supplier_analysis": [
                "creditors",
                "purchase_orders", 
                "stock_management",
                "stock_operations"
            ],
            "key_endpoints_for_stock_analysis": [
                "stock_management",
                "stock_operations", 
                "purchase_orders",
                "creditors",
                "reports"
            ],
            "container_import_tracking": [
                "stock_operations",
                "purchase_orders",
                "creditors"
            ]
        }
    }

if __name__ == "__main__":
    # Generate comprehensive endpoints data
    endpoints_data = build_comprehensive_endpoints()
    
    # Save to file
    with open("endpoints_comprehensive.json", "w") as f:
        json.dump(endpoints_data, f, indent=2)
    
    # Generate categorized version for analyst
    categorized_data = categorize_endpoints()
    
    # Save categorized version
    with open("endpoints_categorized.json", "w") as f:
        json.dump(categorized_data, f, indent=2)
    
    print(f"Generated comprehensive endpoints file with {endpoints_data['metadata']['total_endpoints']} endpoints")
    print(f"Categories: {', '.join(endpoints_data['metadata']['categories'])}")
    print("\nKey categories for analyst agent:")
    for focus_area, categories in categorized_data["analyst_focus"].items():
        print(f"- {focus_area}: {', '.join(categories)}")
    
    print("\nFiles created:")
    print("- endpoints_comprehensive.json")
    print("- endpoints_categorized.json")
