"""
Reporting service for the Email Order Processor.
Handles all output generation, email notifications, and data export functionality.
"""
import logging
import os
import json
import csv
import smtplib
from datetime import datetime
from typing import List, Optional, Dict, Any
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from base64 import urlsafe_b64encode

from jinja2 import Environment, FileSystemLoader, select_autoescape
from models import ProcessedOrder
from gmail_service import GmailService

logger = logging.getLogger(__name__)


class ReportingService:
    """Service for handling all reporting and output generation."""
    
    def __init__(self):
        """Initialize the reporting service with Jinja2 template environment."""
        self.gmail_service = GmailService()
        
        # Setup Jinja2 environment
        template_dir = os.path.join(os.path.dirname(__file__), 'templates')
        self.jinja_env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=select_autoescape(['html', 'xml'])
        )
        
        logger.info("Reporting service initialized with Jinja2 templates")
    
    def generate_html_completion_email(self, processed_orders: List[ProcessedOrder]) -> str:
        """Generate a professional HTML email using Jinja2 template."""
        try:
            template = self.jinja_env.get_template('email_report.html')
            
            # Prepare template data
            current_time = datetime.now().strftime("%B %d, %Y at %I:%M %p")
            
            # Calculate summary stats with error handling
            try:
                processed_count = sum(1 for order in processed_orders if hasattr(order, 'extracted_data') and order.extracted_data)
                review_count = len(processed_orders) - processed_count
                success_rate = (processed_count/len(processed_orders)*100) if processed_orders else 0
            except Exception as e:
                logger.error(f"Error calculating summary stats: {e}")
                processed_count = 0
                review_count = len(processed_orders) if processed_orders else 0
                success_rate = 0
            
            # Prepare email data for template
            email_data = []
            for i, order in enumerate(processed_orders):
                try:
                    email_info = self._extract_email_info(order)
                    email_data.append(email_info)
                except Exception as e:
                    logger.error(f"Error processing order {i} in HTML generation: {e}")
                    # Add error entry
                    email_data.append({
                        'timestamp': 'Error',
                        'sender': 'Error',
                        'subject': f'Error processing email #{i+1}',
                        'summary': f'Error: {str(e)[:100]}',
                        'status': 'Error',
                        'status_class': 'status-error',
                        'row_bg': '#fef2f2'
                    })
            
            template_data = {
                'current_time': current_time,
                'total_emails': len(processed_orders),
                'processed_count': processed_count,
                'review_count': review_count,
                'success_rate': success_rate,
                'emails': email_data,
                'has_emails': bool(processed_orders)
            }
            
            return template.render(**template_data)
            
        except Exception as e:
            logger.error(f"Error generating HTML email with template: {e}")
            # Fallback to simple HTML
            return self._generate_fallback_html(processed_orders)
    
    def _extract_email_info(self, order: ProcessedOrder) -> Dict[str, Any]:
        """Extract email information for template rendering."""
        email_data = order.email_data
        is_processed = order.extracted_data is not None
        
        # Format timestamp with error handling
        try:
            if hasattr(email_data, 'timestamp') and email_data.timestamp:
                timestamp = datetime.fromisoformat(email_data.timestamp.replace('Z', '+00:00')).strftime('%m/%d %H:%M')
            else:
                timestamp = 'Unknown'
        except Exception:
            timestamp = email_data.timestamp[:10] if hasattr(email_data, 'timestamp') and email_data.timestamp else 'Unknown'
        
        # Extract sender name (remove email part) with error handling
        sender = getattr(email_data, 'sender', 'Unknown')
        if sender and '<' in sender and '>' in sender:
            sender = sender.split('<')[0].strip()
        elif sender and '@' in sender:
            sender = sender.split('@')[0]
        
        # Truncate subject if too long with error handling
        subject = getattr(order, 'email_subject', 'No Subject')
        if subject and len(subject) > 50:
            subject = subject[:47] + "..."
        
        # Get meaningful summary with error handling
        try:
            summary_text = self._extract_meaningful_summary(
                getattr(order, 'markdown_summary', ''), 
                order.extracted_data
            )
        except Exception as e:
            logger.debug(f"Error extracting summary: {e}")
            summary_text = "Summary extraction failed"
        
        # Status and styling
        if is_processed:
            status_text = "✓ Processed"
            status_class = "status-processed"
            row_bg = "#f0fdf4"  # Light green
        else:
            status_text = "⚠ Review"
            status_class = "status-review"  
            row_bg = "#fffbeb"  # Light yellow
        
        return {
            'timestamp': timestamp,
            'sender': sender,
            'subject': subject,
            'summary': summary_text,
            'status': status_text,
            'status_class': status_class,
            'row_bg': row_bg
        }
    
    def _extract_meaningful_summary(self, markdown_summary: str, extracted_data=None) -> str:
        """Extract a meaningful summary from the markdown content."""
        if not markdown_summary:
            return "No summary available"
        
        # If we have extracted order data, create a concise summary
        if extracted_data:
            try:
                customer_id = extracted_data.customer_details.debtor_id
                po_number = extracted_data.customer_details.customer_order_number or "N/A"
                line_count = len(extracted_data.order_lines)
                return f"Order from Customer {customer_id}, PO: {po_number}, {line_count} item(s)"
            except Exception as e:
                logger.debug(f"Error creating order summary: {e}")
                pass
        
        # Extract meaningful content from markdown
        lines = [line.strip() for line in markdown_summary.splitlines() if line.strip()]
        
        # Skip common headers and look for actual content
        meaningful_lines = []
        skip_patterns = [
            "# Email Summary", "## Email Summary", "# Order Summary", "## Order Summary",
            "Based on the", "I'll analyze", "Here's a", "The email", "This email",
            "## Order Summary", "## Products", "## Delivery", "## Special"
        ]
        
        for line in lines:
            # Skip headers, empty lines, and common LLM intro phrases
            if (line.startswith('#') or 
                any(pattern in line for pattern in skip_patterns) or
                line.startswith('---') or
                line.startswith('*Generated by') or
                len(line) < 10):
                continue
            
            # Look for bullet points with meaningful content
            if line.startswith('- **') and ':' in line:
                # Extract value after colon
                value = line.split(':', 1)[1].strip()
                if value and value not in ['N/A', 'None', 'Unknown']:
                    meaningful_lines.append(value)
            elif line.startswith('- ') or line.startswith('* '):
                meaningful_lines.append(line[2:].strip())
            elif ':' in line and not line.startswith('##'):
                meaningful_lines.append(line)
                
            if len(meaningful_lines) >= 3:  # Get first 3 meaningful points
                break
        
        if meaningful_lines:
            # Join the meaningful lines and truncate if too long
            summary = '; '.join(meaningful_lines)
            if len(summary) > 150:
                summary = summary[:147] + "..."
            return summary
        
        # Fallback: use first substantial non-header line
        for line in lines:
            if (not line.startswith('#') and 
                not line.startswith('---') and 
                not any(pattern in line for pattern in skip_patterns) and
                len(line) > 20):
                return line[:150] + "..." if len(line) > 150 else line
        
        return "Email content processed"
    
    def _generate_fallback_html(self, processed_orders: List[ProcessedOrder]) -> str:
        """Generate simple fallback HTML if template fails."""
        current_time = datetime.now().strftime("%B %d, %Y at %I:%M %p")
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Order Processing Complete</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #2563eb; color: white; padding: 20px; text-align: center; }}
                .summary {{ background-color: #f3f4f6; padding: 15px; margin: 20px 0; }}
                .email-list {{ margin: 20px 0; }}
                .email-item {{ padding: 10px; border-bottom: 1px solid #e5e7eb; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>TeamsysV0.1 - Processing Complete</h1>
                <p>Run Time: {current_time}</p>
            </div>
            <div class="summary">
                <p><strong>Total Emails:</strong> {len(processed_orders)}</p>
                <p><strong>Status:</strong> Processing completed</p>
            </div>
            <div class="email-list">
                <h2>Processed Emails</h2>
        """
        
        for order in processed_orders:
            subject = getattr(order, 'email_subject', 'No Subject')
            status = "Processed" if order.extracted_data else "Review"
            html += f"""
                <div class="email-item">
                    <strong>{subject}</strong> - {status}
                </div>
            """
        
        html += """
            </div>
        </body>
        </html>
        """
        
        return html
    
    def send_completion_email(self, subject: str, processed_orders: List[ProcessedOrder], 
                            recipient: Optional[str] = None, body: Optional[str] = None):
        """Send completion email notification via Gmail API."""
        try:
            # Use provided recipient or default from environment
            if not recipient:
                recipient = os.getenv('LOG_RECIPIENT_EMAIL')
                if not recipient:
                    logger.warning("No email recipient specified and LOG_RECIPIENT_EMAIL not set")
                    return
            
            # Generate HTML body if not provided
            if not body:
                body = self.generate_html_completion_email(processed_orders)
            
            # Create multipart message for HTML
            message = MIMEMultipart('alternative')
            message['to'] = recipient
            message['from'] = os.getenv('USER_EMAIL', 'me')
            message['subject'] = subject
            
            # Create HTML part
            html_part = MIMEText(body, 'html')
            message.attach(html_part)
            
            raw = urlsafe_b64encode(message.as_bytes()).decode()
            send_body = {'raw': raw}
            
            service = getattr(self.gmail_service, 'service', None)
            if service is None:
                logger.error('Gmail API service not initialized. Cannot send notification email.')
                return
                
            service.users().messages().send(userId='me', body=send_body).execute()
            logger.info(f'HTML notification email sent to {recipient} via Gmail API')
            
        except Exception as e:
            logger.error(f'Failed to send notification email via Gmail API: {e}')
    
    def send_error_notification(self, subject: str, error: str, recipient: Optional[str] = None):
        """Send error notification email."""
        try:
            # Use provided recipient or default from environment
            if not recipient:
                recipient = os.getenv('LOG_RECIPIENT_EMAIL')
                if not recipient:
                    logger.warning("No email recipient specified and LOG_RECIPIENT_EMAIL not set")
                    return
            
            # Generate error email HTML
            error_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .error-container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .error-header {{ background-color: #f44336; color: white; padding: 20px; text-align: center; border-radius: 5px; }}
                    .error-content {{ background-color: #fff3cd; padding: 20px; margin-top: 20px; border-radius: 5px; border-left: 4px solid #f44336; }}
                    .error-details {{ background-color: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; }}
                </style>
            </head>
            <body>
                <div class="error-container">
                    <div class="error-header">
                        <h1>Email Extraction Failed</h1>
                        <p>TeamsysV0.1 encountered an error during processing</p>
                    </div>
                    <div class="error-content">
                        <h3>Error Details:</h3>
                        <div class="error-details">{error}</div>
                        <p><strong>Time:</strong> {datetime.now().strftime("%B %d, %Y at %I:%M %p")}</p>
                        <p>Please check the logs for more detailed information.</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            self.send_completion_email(subject, [], recipient, error_html)
            
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")
    
    def export_results_csv(self, processed_orders: List[ProcessedOrder], filename: str = ""):
        """Export processing results to CSV."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"email_processing_results_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'email_id', 'email_subject', 'debtor_id', 'customer_po', 
                    'order_status', 'ship_via', 'line_items_count', 
                    'markdown_file', 'myob_file', 'processed_time'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for order in processed_orders:
                    if order.extracted_data:
                        customer = order.extracted_data.customer_details
                        debtor_id = customer.debtor_id
                        customer_po = customer.customer_order_number or ''
                        order_status = order.extracted_data.order_status
                        ship_via = order.extracted_data.X_SHIPVIA or ''
                        line_items_count = len(order.extracted_data.order_lines)
                    else:
                        debtor_id = 'Review'
                        customer_po = ''
                        order_status = 'Review'
                        ship_via = ''
                        line_items_count = 0
                    
                    writer.writerow({
                        'email_id': order.email_id,
                        'email_subject': order.email_subject,
                        'debtor_id': debtor_id,
                        'customer_po': customer_po,
                        'order_status': order_status,
                        'ship_via': ship_via,
                        'line_items_count': line_items_count,
                        'markdown_file': getattr(order, 'markdown_filepath', ''),
                        'myob_file': getattr(order, 'myob_filepath', ''),
                        'processed_time': datetime.now().isoformat()
                    })
            
            print(f"Results exported to CSV: {filename}")
            logger.info(f"CSV export completed: {filename}")
            
        except Exception as e:
            print(f"ERROR: Failed to export CSV: {e}")
            logger.error(f"CSV export failed: {e}")
    
    def export_results_json(self, processed_orders: List[ProcessedOrder], filename: str = ""):
        """Export processing results to JSON."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"email_processing_results_{timestamp}.json"
        
        try:
            results = []
            for order in processed_orders:
                if order.extracted_data:
                    customer = order.extracted_data.customer_details
                    line_items = [
                        {
                            'item_number': item.item_number,
                            'description': item.description,
                            'quantity': item.quantity,
                            'unit_price': item.unit_price
                        }
                        for item in order.extracted_data.order_lines
                    ]
                    
                    order_data = {
                        'email_id': order.email_id,
                        'email_subject': order.email_subject,
                        'customer': {
                            'debtor_id': customer.debtor_id,
                            'customer_po': customer.customer_order_number,
                            'company_name': customer.company_name
                        },
                        'order_details': {
                            'status': order.extracted_data.order_status,
                            'ship_via': order.extracted_data.X_SHIPVIA,
                            'line_items': line_items
                        },
                        'files': {
                            'markdown': getattr(order, 'markdown_filepath', ''),
                            'myob': getattr(order, 'myob_filepath', '')
                        },
                        'processed_time': datetime.now().isoformat()
                    }
                else:
                    order_data = {
                        'email_id': order.email_id,
                        'email_subject': order.email_subject,
                        'status': 'Review',
                        'files': {
                            'markdown': getattr(order, 'markdown_filepath', ''),
                            'myob': getattr(order, 'myob_filepath', '')
                        },
                        'processed_time': datetime.now().isoformat()
                    }
                
                results.append(order_data)
            
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump({
                    'export_time': datetime.now().isoformat(),
                    'total_orders': len(processed_orders),
                    'orders': results
                }, jsonfile, indent=2)
            
            print(f"Results exported to JSON: {filename}")
            logger.info(f"JSON export completed: {filename}")
            
        except Exception as e:
            print(f"ERROR: Failed to export JSON: {e}")
            logger.error(f"JSON export failed: {e}")
    
    def show_processing_stats(self, processed_orders: List[ProcessedOrder]):
        """Display detailed processing statistics."""
        if not processed_orders:
            print("\nNo orders to analyze")
            return
        
        print(f"\n{'='*60}")
        print("PROCESSING STATISTICS")
        print(f"{'='*60}")
        
        # Basic counts
        total_orders = len(processed_orders)
        processed_count = sum(1 for order in processed_orders if order.extracted_data)
        review_count = total_orders - processed_count
        
        print(f"Total Emails Processed: {total_orders}")
        print(f"Orders Successfully Extracted: {processed_count}")
        print(f"Emails Requiring Review: {review_count}")
        print(f"Success Rate: {(processed_count/total_orders*100):.1f}%")
        
        # Order status breakdown
        status_counts = {}
        for order in processed_orders:
            if order.extracted_data:
                status = order.extracted_data.order_status
                status_counts[status] = status_counts.get(status, 0) + 1
            else:
                status_counts['Review'] = status_counts.get('Review', 0) + 1
        
        print(f"\nOrder Status Breakdown:")
        for status, count in sorted(status_counts.items()):
            print(f"  {status}: {count} orders")
        
        # Shipping method breakdown
        ship_via_counts = {}
        for order in processed_orders:
            if order.extracted_data:
                ship_via = order.extracted_data.X_SHIPVIA or "Not specified"
                ship_via_counts[ship_via] = ship_via_counts.get(ship_via, 0) + 1
            else:
                ship_via_counts["Review"] = ship_via_counts.get("Review", 0) + 1
        
        print(f"\nShipping Method Breakdown:")
        for ship_via, count in sorted(ship_via_counts.items()):
            print(f"  {ship_via}: {count} orders")
        
        print("="*60)
    
    def _display_intelligent_results(self, classifications, processed_orders, dashboard_report, actionable_insights):
        """Display results from intelligent workflow processing."""
        print(f"\n🎯 INTELLIGENT PROCESSING RESULTS")
        print("="*60)
        
        # Category breakdown
        print(f"📊 EMAIL CATEGORIES:")
        for category, count in dashboard_report.get('category_breakdown', {}).items():
            print(f"  {category}: {count} emails")
        
        # Priority breakdown
        print(f"\n⚡ PRIORITY BREAKDOWN:")
        for priority, count in dashboard_report.get('priority_breakdown', {}).items():
            print(f"  {priority}: {count} emails")
        
        # Actionable insights
        print(f"\n💡 ACTIONABLE INSIGHTS:")
        for insight in actionable_insights:
            print(f"  • {insight}")
        
        # Processing summary
        order_count = len([o for o in processed_orders if o.extracted_data])
        non_order_count = len(processed_orders) - order_count
        
        print(f"\n📦 PROCESSING SUMMARY:")
        print(f"  Orders Processed: {order_count}")
        print(f"  Non-Orders Processed: {non_order_count}")
        print(f"  Total Emails: {len(processed_orders)}")
        
        print("="*60)