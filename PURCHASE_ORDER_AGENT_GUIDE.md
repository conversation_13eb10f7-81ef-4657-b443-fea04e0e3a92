# MYOB Purchase Order Agent - Usage Guide

## Overview
The MYOB Purchase Order Agent is a dynamic AI-powered system that analyzes stock levels, forecasts demand, and generates purchase order recommendations for overseas suppliers using the MYOB EXO ERP API and Mistral AI.

## Features

### 🔍 **Dynamic Endpoint Discovery**
- Automatically discovers all available MYOB EXO endpoints
- Categorizes endpoints by function (stock, purchase orders, suppliers, etc.)
- Analyzes purchase order capabilities and limitations
- Creates adaptive workflows based on discovered features

### 📊 **Enhanced Stock Analysis**
- Analyzes current stock levels against reorder points
- Identifies critical, high, medium, and low priority items
- Calculates days until stockout
- Retrieves comprehensive stock data from multiple endpoints
- Uses AI to provide enhanced insights and priority adjustments

### 🤖 **AI-Powered Forecasting**
- Uses Mistral AI for demand forecasting
- Analyzes historical usage patterns
- Considers seasonal trends and market conditions
- Provides intelligent daily usage estimates
- Adapts forecasting based on available data sources

### � **Dynamic Purchase Order Recommendations**
- Groups items by supplier for efficient ordering
- Optimizes order quantities using AI analysis
- Considers minimum order quantities, volume discounts, and lead times
- Provides risk assessment for each supplier
- Creates adaptive workflows based on system capabilities

### � **Adaptive Implementation Planning**
- Generates comprehensive implementation plans
- Includes prioritized action steps and timelines
- Addresses budget and cash flow considerations
- Provides performance metrics and contingency plans
- Adapts plans based on discovered system capabilities

## Setup

### 1. Environment Variables
Add these to your `.env` file:

```env
# Mistral AI Configuration
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-large-latest

# MYOB EXO Configuration (existing)
EXO_IP=your_myob_server_ip
EXO_PORT=8888
USER=your_myob_username
PWD=your_myob_password
API_KEY=your_myob_api_key
EXO_TOK=your_myob_token

# Supabase Configuration (for memory storage)
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Get Mistral API Key
1. Sign up at https://mistral.ai/
2. Generate an API key
3. Add it to your environment variables

## Usage

### Basic Usage
```python
from byejarrod import MyobPurchaseOrderAgent

# Initialize the agent
agent = MyobPurchaseOrderAgent()

# Run full analysis
results = agent.run_full_analysis()

# Print summary
print(f"Total Items Analyzed: {results['summary']['total_items_analyzed']}")
print(f"Total Recommendations: {results['summary']['total_recommendations']}")
print(f"Total Value: ${results['summary']['total_value']:,.2f}")
```

### Advanced Usage
```python
# Test endpoint discovery
discovery_results = agent.discover_endpoints()
print(f"Discovered {len(discovery_results.get('endpoints', []))} endpoints")

# Analyze capabilities
capabilities = agent.discover_purchase_order_capabilities()
print(f"Can create POs: {capabilities.get('can_create_purchase_orders')}")

# Get enhanced stock analysis
enhanced_analysis = agent.get_enhanced_stock_analysis("ITEM001")
print(f"AI Insights: {enhanced_analysis.get('ai_insights')}")

# Create dynamic workflow
workflow = agent.create_dynamic_purchase_order_workflow(recommendations)
print(f"Workflow steps: {len(workflow.get('workflow_steps', []))}")

# Use adaptive stock retrieval
stock_items = agent.get_adaptive_stock_items()
print(f"Retrieved {len(stock_items)} items using adaptive method")
```

### Testing Discovery Features
```python
# Run discovery tests
python test_discovery.py

# This will test:
# - Endpoint discovery
# - Capability analysis
# - Adaptive stock retrieval
# - Enhanced stock analysis
# - Dynamic workflow creation
```

### Command Line Usage
```bash
# Run from command line
python byejarrod.py
```

## Data Structures

### StockAnalysis
```python
@dataclass
class StockAnalysis:
    stockcode: str
    description: str
    current_stock: float
    reorder_level: float
    reorder_quantity: float
    cost_price: float
    supplier_id: str
    supplier_name: str
    lead_time_days: int
    priority: PriorityLevel  # CRITICAL, HIGH, MEDIUM, LOW
    forecast_demand: float
    recommended_order_qty: float
    days_until_stockout: int
    seasonal_factor: float
```

### PurchaseOrderRecommendation
```python
@dataclass
class PurchaseOrderRecommendation:
    supplier_id: str
    supplier_name: str
    total_value: float
    line_items: List[Dict[str, Any]]
    priority: PriorityLevel
    justification: str
    expected_delivery_date: str
    risk_assessment: str
```

## API Methods

### Discovery Methods
- `discover_endpoints()` - Discover all available MYOB EXO endpoints
- `analyze_available_endpoints()` - Categorize endpoints by function
- `discover_purchase_order_capabilities()` - Analyze PO creation capabilities
- `get_enhanced_stock_data(stockcode)` - Get comprehensive stock data
- `get_enhanced_stock_analysis(stockcode)` - Get AI-enhanced stock analysis

### Adaptive Methods
- `get_adaptive_stock_items(filters=None)` - Retrieve stock using best endpoints
- `create_dynamic_purchase_order_workflow(recommendations)` - Create adaptive workflow
- `_adjust_priority_with_ai(priority, insights)` - AI-driven priority adjustment

### Core Methods
- `analyze_stock_levels(include_all=False)` - Analyze stock levels
- `generate_purchase_order_recommendations(stock_analysis)` - Generate PO recommendations
- `generate_implementation_plan(recommendations)` - Create implementation plan
- `run_full_analysis()` - Complete analysis workflow

### Utility Methods
- `get_stock_items(filters=None)` - Retrieve stock items from MYOB
- `get_stock_report(stockcode)` - Get detailed stock report
- `estimate_daily_usage(stockcode)` - Estimate daily usage
- `forecast_demand(stockcode, days_ahead=30)` - Forecast demand

### AI Methods
- `call_mistral_api(messages, temperature=0.3)` - Call Mistral AI API

## Output Format

The `run_full_analysis()` method returns a comprehensive dictionary with:

```python
{
    "analysis_date": "2025-07-11T10:30:00",
    "discovery_summary": {
        "total_endpoints": 45,
        "stock_endpoints": 8,
        "purchase_order_endpoints": 6,
        "supplier_endpoints": 4,
        "can_create_purchase_orders": true,
        "can_validate_purchase_orders": true
    },
    "stock_analysis": [...],  # List of StockAnalysis objects
    "recommendations": [...],  # List of PurchaseOrderRecommendation objects
    "implementation_plan": "...",  # Detailed implementation plan
    "dynamic_workflow": {
        "workflow_steps": [...],
        "required_data": [...],
        "validation_steps": [...],
        "ai_workflow_optimization": "..."
    },
    "discovered_capabilities": {...},  # Detailed capability analysis
    "summary": {
        "total_items_analyzed": 45,
        "total_recommendations": 8,
        "total_value": 125000.00,
        "critical_items": 5,
        "high_priority_items": 12
    }
}
```

## Integration Points

### MYOB EXO API Endpoints Used
- `/stockitem` - Retrieve stock items
- `/stockitem/{stockcode}/report` - Get stock reports

### Memory Storage
- Results are stored in Supabase for future reference
- Historical data is used for improved forecasting
- Implementation plans are saved for tracking

## Error Handling

The agent includes comprehensive error handling:
- API connection failures
- Invalid stock codes
- Missing configuration
- AI API errors
- Memory storage issues

## Performance Considerations

- Stock analysis is optimized to process items in batches
- AI calls are made efficiently with proper rate limiting
- Results are cached in memory for faster subsequent runs
- Large datasets are processed incrementally

## Best Practices

1. **Regular Analysis**: Run analysis daily or weekly
2. **Review Recommendations**: Always review AI recommendations before acting
3. **Monitor Performance**: Track the accuracy of forecasts
4. **Update Parameters**: Adjust reorder levels based on results
5. **Backup Data**: Ensure memory storage is backed up

## Troubleshooting

### Common Issues
1. **Mistral API Key Not Found**: Ensure `MISTRAL_API_KEY` is set
2. **MYOB Connection Failed**: Check network and credentials
3. **No Stock Items Retrieved**: Verify MYOB API access
4. **Memory Storage Issues**: Check Supabase configuration

### Debug Mode
Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

- Integration with additional ERP systems
- Support for multiple AI models
- Advanced forecasting algorithms
- Real-time inventory monitoring
- Mobile app integration
- Automated purchase order creation

## Support

For issues or questions:
1. Check the error logs
2. Verify configuration settings
3. Test API connections
4. Review memory storage status
