"""
<PERSON><PERSON>t to update location IDs for orders that were posted with incorrect location IDs.
"""
import os
import json
from datetime import datetime
from myob_poster import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def get_all_order_files(directory="myob"):
    """
    Get all order files from the specified directory.

    Args:
        directory: Directory containing order files

    Returns:
        List of all JSON filenames in the directory
    """
    if not os.path.exists(directory):
        print(f"Directory {directory} does not exist")
        return []

    # Get all JSON files
    files = []
    for filename in os.listdir(directory):
        if filename.endswith(".json"):
            files.append(filename)

    return files

def update_location_id(order_data, new_location_id=1):
    """
    Update the location ID in the order data.

    Args:
        order_data: Dictionary containing order data
        new_location_id: The new location ID to set

    Returns:
        Updated order data dictionary
    """
    # Update location ID in the main order
    if "locationid" in order_data:
        order_data["locationid"] = new_location_id

    # Update location ID in each line item if present
    if "lines" in order_data:
        for line in order_data["lines"]:
            if "locationid" in line:
                line["locationid"] = new_location_id

    return order_data

def main():
    """
    Main function to update location IDs for recent orders.
    """
    poster = MYOBPoster()
    all_orders = get_all_order_files()

    if not all_orders:
        print("No orders found to update")
        return

    print(f"Found {len(all_orders)} orders to update")

    success_count = 0
    failure_count = 0

    for filename in all_orders:
        order_id = filename.replace(".json", "")
        print(f"\nProcessing order: {order_id}")

        # Load the order data
        order_data = poster.load_order(order_id)
        if not order_data:
            print(f"Failed to load order {order_id}")
            failure_count += 1
            continue

        # Update the location ID
        updated_data = update_location_id(order_data, new_location_id=1)

        # Edit the posted order
        success, message = poster.edit_posted_order(order_id, updated_data)

        if success:
            print(f"Successfully updated order {order_id}")
            success_count += 1
        else:
            print(f"Failed to update order {order_id}: {message}")
            failure_count += 1

    print("\nUpdate Summary:")
    print(f"Total orders processed: {len(recent_orders)}")
    print(f"Successfully updated: {success_count}")
    print(f"Failed to update: {failure_count}")

if __name__ == "__main__":
    main()
