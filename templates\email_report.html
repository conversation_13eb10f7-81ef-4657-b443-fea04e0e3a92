<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Order Processing Complete</title>
    <style type="text/css">
      body,
      table,
      td,
      a {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }
      table,
      td {
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }
      img {
        -ms-interpolation-mode: bicubic;
        border: 0;
        height: auto;
        line-height: 100%;
        outline: none;
        text-decoration: none;
      }
      body {
        height: 100% !important;
        margin: 0;
        padding: 0;
        width: 100% !important;
      }

      .status-processed {
        color: #10b981;
        font-weight: bold;
      }
      .status-review {
        color: #f59e0b;
        font-weight: bold;
      }
      .status-error {
        color: #dc2626;
        font-weight: bold;
      }
      .email-summary {
        word-wrap: break-word;
        font-size: 11px;
        line-height: 1.3;
      }
      .email-from {
        word-wrap: break-word;
        font-size: 11px;
        max-width: 200px;
      }
      .email-subject {
        word-wrap: break-word;
        font-size: 11px;
        line-height: 1.3;
      }
      .email-time {
        font-size: 10px;
        white-space: nowrap;
      }
      .email-date {
        font-size: 10px;
        white-space: nowrap;
      }

      @media screen and (max-width: 1300px) {
        .main-table {
          width: 100% !important;
          min-width: 1200px !important;
        }
      }

      /* Color scheme variables */
      :root {
        --primary-color: #2563eb;
        --text-color: #1f2937;
        --border-color: #e5e7eb;
        --background-color: #f3f4f6;
        --success-color: #10b981;
        --warning-color: #f59e0b;
      }
    </style>
  </head>
  <body
    style="
      background-color: #f3f4f6;
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
    "
  >
    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center" style="padding: 20px">
          <table
            border="0"
            cellpadding="20"
            cellspacing="0"
            width="1200"
            style="
              background-color: #ffffff;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              min-width: 1200px;
            "
          >
            <!-- Header Section -->
            <tr>
              <td align="center">
                <h1 style="color: #2563eb; margin: 0 0 20px 0; font-size: 24px">
                  TeamsysV0.1 - Processing Complete
                </h1>
                <table border="0" cellpadding="10" cellspacing="0" width="100%">
                  <tr>
                    <td
                      align="center"
                      style="background-color: #f3f4f6; border-radius: 4px"
                    >
                      <strong>Run Time:</strong> {{ current_time }}
                    </td>
                  </tr>
                </table>
                <br />

                <!-- Summary Statistics -->
                <table
                  border="0"
                  cellpadding="10"
                  cellspacing="10"
                  width="100%"
                >
                  <tr>
                    <td
                      align="center"
                      style="
                        background-color: #10b981;
                        color: white;
                        border-radius: 4px;
                        width: 25%;
                      "
                    >
                      <strong>{{ total_emails }}</strong><br />
                      <small>Total Emails</small>
                    </td>
                    <td
                      align="center"
                      style="
                        background-color: #2563eb;
                        color: white;
                        border-radius: 4px;
                        width: 25%;
                      "
                    >
                      <strong>{{ processed_count }}</strong><br />
                      <small>Orders Extracted</small>
                    </td>
                    <td
                      align="center"
                      style="
                        background-color: #f59e0b;
                        color: white;
                        border-radius: 4px;
                        width: 25%;
                      "
                    >
                      <strong>{{ review_count }}</strong><br />
                      <small>Need Review</small>
                    </td>
                    <td
                      align="center"
                      style="
                        background-color: #1f2937;
                        color: white;
                        border-radius: 4px;
                        width: 25%;
                      "
                    >
                      <strong>{{ "%.0f"|format(success_rate) }}%</strong><br />
                      <small>Success Rate</small>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Email Details Section -->
            <tr>
              <td>
                <h2
                  style="
                    color: #1f2937;
                    border-bottom: 2px solid #e5e7eb;
                    padding-bottom: 10px;
                    margin: 20px 0 10px 0;
                  "
                >
                  Email Processing Details
                </h2>

                {% if has_emails %}
                <table
                  border="0"
                  cellpadding="8"
                  cellspacing="0"
                  width="100%"
                  style="
                    border-collapse: collapse;
                    font-size: 12px;
                    table-layout: fixed;
                  "
                >
                  <thead>
                    <tr style="background-color: #f3f4f6">
                      <th
                        style="
                          border: 1px solid #e5e7eb;
                          text-align: left;
                          padding: 10px;
                          font-weight: bold;
                          width: 12%;
                        "
                      >
                        Date/Time
                      </th>
                      <th
                        style="
                          border: 1px solid #e5e7eb;
                          text-align: left;
                          padding: 10px;
                          font-weight: bold;
                          width: 20%;
                        "
                      >
                        From
                      </th>
                      <th
                        style="
                          border: 1px solid #e5e7eb;
                          text-align: left;
                          padding: 10px;
                          font-weight: bold;
                          width: 25%;
                        "
                      >
                        Subject
                      </th>
                      <th
                        style="
                          border: 1px solid #e5e7eb;
                          text-align: left;
                          padding: 10px;
                          font-weight: bold;
                          width: 33%;
                        "
                      >
                        Summary
                      </th>
                      <th
                        style="
                          border: 1px solid #e5e7eb;
                          text-align: left;
                          padding: 10px;
                          font-weight: bold;
                          width: 10%;
                        "
                      >
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for email in emails %}
                    <tr style="background-color: {{ email.row_bg }};">
                      <td
                        style="
                          border: 1px solid #e5e7eb;
                          padding: 8px;
                          vertical-align: top;
                          max-width: 120px;
                          word-wrap: break-word;
                        "
                      >
                        {{ email.timestamp }}
                      </td>
                      <td
                        style="
                          border: 1px solid #e5e7eb;
                          padding: 8px;
                          vertical-align: top;
                          max-width: 150px;
                          word-wrap: break-word;
                        "
                      >
                        {{ email.sender }}
                      </td>
                      <td
                        style="
                          border: 1px solid #e5e7eb;
                          padding: 8px;
                          vertical-align: top;
                          max-width: 200px;
                          word-wrap: break-word;
                        "
                      >
                        {{ email.subject }}
                      </td>
                      <td
                        style="
                          border: 1px solid #e5e7eb;
                          padding: 8px;
                          vertical-align: top;
                          max-width: 200px;
                          word-wrap: break-word;
                        "
                        class="email-summary"
                      >
                        {{ email.summary }}
                      </td>
                      <td
                        style="
                          border: 1px solid #e5e7eb;
                          padding: 8px;
                          vertical-align: top;
                        "
                        class="{{ email.status_class }}"
                      >
                        {{ email.status }}
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
                {% else %}
                <table border="0" cellpadding="0" cellspacing="0" width="100%">
                  <tr>
                    <td
                      align="center"
                      style="padding: 30px; color: #1f2937; font-style: italic"
                    >
                      No emails were processed in this run.
                    </td>
                  </tr>
                </table>
                {% endif %}
              </td>
            </tr>

            <!-- Footer Section -->
            <tr>
              <td
                style="
                  padding-top: 20px;
                  border-top: 1px solid #e5e7eb;
                  text-align: center;
                  color: #1f2937;
                  font-size: 12px;
                "
              >
                <p style="margin: 5px 0">
                  Files saved to: <strong>markdown/</strong> and
                  <strong>myob/</strong> directories
                </p>
                <p style="margin: 5px 0; font-style: italic">
                  Generated by TeamsysV0.1 Email Order Processor
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
