# Main Processor CLI Quick Reference Guide

## 🚀 Overview
The `main_processor.py` script processes Gmail emails with PDF attachments, extracts order data, and generates markdown summaries and MYOB payloads. This guide covers all available CLI options.

## 📋 Basic Usage

### Default Execution
```bash
# Continuous polling (default: every 5 minutes, all configured labels)
.venv\Scripts\python.exe main_processor.py

# Single run
.venv\Scripts\python.exe main_processor.py --once
```

## 🔧 Execution Modes

| Command | Description |
|---------|-------------|
| `--once` | Run once instead of continuous polling |
| `--poll-interval N` | Polling interval in minutes (default: 5) |
| `--dry-run` | Preview what would be processed without actual processing |

### Examples:
```bash
# Poll every 60 seconds (1 minute)
.venv\Scripts\python.exe main_processor.py --poll-interval 1

# Single run with preview
.venv\Scripts\python.exe main_processor.py --once --dry-run
```

## 📧 Email Filtering & Selection

### Label Management
| Command | Description |
|---------|-------------|
| `--labels LABEL1 LABEL2` | Specify Gmail labels to process |
| `--exclude-labels LABEL1 LABEL2` | Exclude specific labels |
| `--list-labels` | List all available Gmail labels and exit |

### Email Limits
| Command | Description |
|---------|-------------|
| `--max-emails N` | Maximum emails per label (default: 2) |
| `--max-total-emails N` | Maximum total emails across all labels |

### Advanced Filtering
| Command | Description |
|---------|-------------|
| `--time-filter SPEC` | Time filter for emails |
| `--unread-only` | Process only unread emails |
| `--custom-query "text"` | Additional Gmail search query |
| `--email-ids ID1 ID2` | Process specific email IDs directly |

### Time Filter Examples:
```bash
# Last 7 days
.venv\Scripts\python.exe main_processor.py --once --time-filter 7d

# Last 2 weeks
.venv\Scripts\python.exe main_processor.py --once --time-filter 2w

# Last 24 hours
.venv\Scripts\python.exe main_processor.py --once --time-filter 24h

# Specific date range
.venv\Scripts\python.exe main_processor.py --once --time-filter "after:2024/01/01 before:2024/01/31"
```

## 🎯 Processing Control

| Command | Description |
|---------|-------------|
| `--include-brady-packing` | Include Brady packing slip emails (normally skipped) |
| `--review-all-emails` | Review ALL emails in label (not just those with PDF attachments) |
| `--force-reprocess` | Reprocess emails with "Processed" labels |
| `--skip-pdf` | Skip PDF attachment processing |
| `--no-labeling` | Skip adding processing labels to Gmail |
| `--parallel N` | Number of parallel processing threads (default: 1) |

### Output Control
| Command | Description |
|---------|-------------|
| `--markdown-only` | Generate only markdown summaries |
| `--myob-only` | Generate only MYOB payloads |
| `--no-files` | Skip saving files to disk |

## 📁 Output & Export Options

### File Management
| Command | Description |
|---------|-------------|
| `--output-dir PATH` | Custom output directory |
| `--export-csv` | Export results to CSV file |
| `--export-json` | Export results to JSON file |
| `--stats` | Show detailed processing statistics |

### Examples:
```bash
# Custom output directory with exports
.venv\Scripts\python.exe main_processor.py --once --output-dir C:\Orders --export-csv --export-json

# Statistics only, no files
.venv\Scripts\python.exe main_processor.py --once --no-files --stats
```

## 📬 Email Notifications

| Command | Description |
|---------|-------------|
| `--no-email` | Skip sending completion emails |
| `--email-recipient EMAIL` | Override notification recipient |
| `--email-subject-prefix "TEXT"` | Add prefix to email subjects |

### Examples:
```bash
# Custom recipient with prefix
.venv\Scripts\python.exe main_processor.py --email-recipient <EMAIL> --email-subject-prefix "[URGENT] "

# No notifications
.venv\Scripts\python.exe main_processor.py --once --no-email
```

## 🔍 Logging & Debugging

| Command | Description |
|---------|-------------|
| `--verbose` / `-v` | Verbose logging |
| `--debug` | Most detailed logging |
| `--quiet` / `-q` | Suppress console output (errors only) |
| `--log-file PATH` | Write logs to file |

### Examples:
```bash
# Debug mode with file logging
.venv\Scripts\python.exe main_processor.py --debug --log-file processing.log

# Quiet mode
.venv\Scripts\python.exe main_processor.py --once --quiet
```

## ⚙️ Configuration & Information

| Command | Description |
|---------|-------------|
| `--list-config` | Show current configuration and exit |
| `--config-file PATH` | Use custom configuration file |
| `--help` | Show detailed help with examples |

## 🎯 Common Use Cases

### 1. **Inbox Monitoring (60 seconds)**
```bash
.venv\Scripts\python.exe main_processor.py --labels Inbox --poll-interval 1
```

### 1a. **Review ALL Inbox Emails (every email, not just PDFs)**
```bash
.venv\Scripts
```

### 2. **Quick Test Run**
```bash
.venv\Scripts\python.exe main_processor.py --once --dry-run --labels Brady RSEA
```

### 3. **Process Last Week's Orders**
```bash
.venv\Scripts\python.exe main_processor.py --once --time-filter 7d --export-csv --stats
```

### 4. **High Volume Processing**
```bash
.venv\Scripts\python.exe main_processor.py --once --max-emails 50 --max-total-emails 200 --parallel 3
```

### 5. **Unread Emails Only**
```bash
.venv\Scripts\python.exe main_processor.py --once --unread-only --export-json
```

### 6. **Custom Query Processing**
```bash
.venv\Scripts\python.exe main_processor.py --once --custom-query "subject:urgent" --email-subject-prefix "[URGENT] "
```

### 7. **Specific Customer Processing**
```bash
.venv\Scripts\python.exe main_processor.py --once --labels Brady --custom-query "from:<EMAIL>"
```

### 8. **Silent Background Processing**
```bash
.venv\Scripts\python.exe main_processor.py --quiet --no-email --log-file background.log
```

### 9. **Development/Testing Mode**
```bash
.venv\Scripts\python.exe main_processor.py --once --dry-run --debug --no-files
```

### 10. **Process Specific Email IDs**
```bash
.venv\Scripts\python.exe main_processor.py --email-ids 18a1b2c3d4e5f6g7 18h1i2j3k4l5m6n7 --export-json
```

## 📊 Default Configuration

- **Labels**: Brady, RSEA, Woolworths, Brierley, Gateway, Highgate, Sitecraft
- **Max Emails**: 2 per label
- **Poll Interval**: 5 minutes
- **Output Directory**: Current directory (markdown/ and myob/ folders)
- **Notifications**: Enabled (uses LOG_RECIPIENT_EMAIL environment variable)
- **Processing Labels**: Enabled (adds Processed/Review/Failed labels to Gmail)

## 🚨 Important Notes

1. **Gmail Authentication**: Ensure Gmail API credentials are properly configured
2. **Environment Variables**: Set `LOG_RECIPIENT_EMAIL` for notifications
3. **Brady Packing Slips**: Automatically skipped unless `--include-brady-packing` is used
4. **Interruption**: Use `Ctrl+C` to stop continuous polling
5. **File Permissions**: Ensure write permissions for output directories
6. **Rate Limits**: Gmail API has rate limits; avoid very frequent polling

## 🔗 Related Files

- `config.py` - Configuration settings
- `gmail_service.py` - Gmail API integration
- `llm_service.py` - LLM processing
- `models.py` - Data models
- `requirements.txt` - Dependencies

---

*Generated for TeamsysV0.1 Email Order Processor*