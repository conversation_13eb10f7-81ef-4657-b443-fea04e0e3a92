"""Payload generator for MYOB-compatible data."""

import logging
from typing import Dict, Any, Optional

from ..models.schemas import ExtractedOrder, MYOBPayload
# Removed GeminiService import - using direct payload generation
from ..core.exceptions import ValidationException

logger = logging.getLogger(__name__)


class PayloadGenerator:
    """Generator for MYOB-compatible payloads."""
    
    def __init__(self):
        # Using direct payload generation - no LLM service needed
        pass
    
    def generate_myob_payload_direct(self, extracted_order: ExtractedOrder) -> Dict[str, Any]:
        """Generate MYOB payload directly from extracted order (no LLM) - more reliable."""
        
        # Build the basic payload
        payload = {
            "debtorid": extracted_order.customer_details.debtor_id,
            "status": extracted_order.order_status or 0,
            "defaultlocationid": 1,
            "lines": [],
        }
        
        # Add customer order number if present
        if extracted_order.customer_details.customer_order_number:
            payload["customerordernumber"] = extracted_order.customer_details.customer_order_number
        
        # Add order lines
        for line in extracted_order.order_lines:
            payload["lines"].append({
                "stockcode": line.stockcode,
                "orderquantity": float(line.orderquantity)
            })
        
        # Add delivery address if present
        if extracted_order.delivery_address:
            addr = extracted_order.delivery_address
            payload["deliveryaddress"] = {
                "line1": addr.line1 or "",
                "line2": addr.line2 or "",
                "line3": addr.line3 or "",
                "line4": addr.line4 or "",
                "line5": addr.line5 or "",
                "line6": addr.line6 or ""
            }
        
        # Add extrafields for shipping method
        extrafields = []
        if extracted_order.X_SHIPVIA:
            extrafields.append({
                "key": "X_SHIPVIA",
                "value": extracted_order.X_SHIPVIA
            })
        
        if extrafields:
            payload["extrafields"] = extrafields
        
        logger.info("Generated minimal MYOB payload successfully")
        return payload
    
    def validate_myob_payload(self, payload: Dict[str, Any]) -> bool:
        """Validate MYOB payload structure."""
        required_fields = ["debtorid", "lines"]
        
        for field in required_fields:
            if field not in payload:
                raise ValidationException(f"Missing required field: {field}")
        
        if not isinstance(payload["lines"], list) or len(payload["lines"]) == 0:
            raise ValidationException("Lines must be a non-empty array")
        
        for i, line in enumerate(payload["lines"]):
            if "stockcode" not in line or "orderquantity" not in line:
                raise ValidationException(f"Line {i+1} missing required fields: stockcode, orderquantity")
        
        # Validate extrafields format if present
        if "extrafields" in payload:
            if not isinstance(payload["extrafields"], list):
                raise ValidationException("extrafields must be an array")
            
            for i, field in enumerate(payload["extrafields"]):
                if not isinstance(field, dict):
                    raise ValidationException(f"extrafields[{i}] must be an object")
                if "key" not in field or "value" not in field:
                    raise ValidationException(f"extrafields[{i}] must have 'key' and 'value' properties")
        
        return True
    
    def create_myob_payload(self, extracted_order: ExtractedOrder) -> MYOBPayload:
        """Create validated MYOB payload from extracted order."""
        payload_dict = self.generate_myob_payload_direct(extracted_order)
        self.validate_myob_payload(payload_dict)
        return MYOBPayload(**payload_dict)