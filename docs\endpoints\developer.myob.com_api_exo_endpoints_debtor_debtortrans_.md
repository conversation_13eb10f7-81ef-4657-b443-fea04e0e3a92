---
url: "https://developer.myob.com/api/exo/endpoints/debtor/debtortrans/"
title: "Debtor Transaction"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Debtor Transaction

Return a debtor transaction.

**Date Released:** Oct 30th 2013 **Date Updated:** July 28th 2014

| URL | Supports |
| --- | --- |
| {URI}/debtor/{debtorid}/transaction | [GET](https://developer.myob.com/api/exo/endpoints/debtor/debtortrans/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/debtor/debtortrans/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/debtor/debtortrans/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

**Note:** Actual fields returned may differ slightly depending on local settings and configuration.

The elements list below details information for Debtor Transaction. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/debtor/debtortrans/#reveal)

#### Attribute Details

- debtorid integer
- Required on PUT
- branchid integer
- Required on PUT
- branch object,null
- Type: object,null
  - name string,null
  - Type: string,null
  - code string,null
  - Type: string,null
  - rel string,null
  - Type: string,null
  - title string,null
  - Type: string,null
  - id integer
  - Required on PUT
  - href string,null
  - Type: string,null
- transactiondate string
- Type: string
- duedate any
- Type: any
- reference1 string,null
- Type: string,null
- reference2 string,null
- Type: string,null
- reference3 string,null
- Type: string,null
- amount number
- Type: number
- allocatedbalance number
- Type: number
- outstandingamount number
- Type: number
- transactiontypeid integer
- Required on PUT
- transactiontype string,null
- Type: string,null
- statusid string,null
- Required on PUT
- status string,null
- Type: string,null
- invoicenumber string,null
- Type: string,null
- extrafields array
- Type: array
- rel string,null
- Type: string,null
- title string,null
- Type: string,null
- id integer
- Required on PUT
- href string,null
- Type: string,null

#### Example json GET response

- {
  - invoicenumber : 10003
  - lines
    - {
      - 0
        - {
          - stockcode : AIRFIL01
          - quantity : 3
          - unitprice : 55.27
          - discount : 0
          - tax : 20.73
          - total : 165.81
          - totalincludingtax : 165.81
          - taxrate : 12.5
          - unitpriceincludingtax : 55.27
          - exchangerate : 0.8
          - hidden\_sellprice : 0
          - description : OVALCHROME AIR FILTER
          - id : 8
          - href
        - }
      - 1
        - {
          - stockcode : AIRPRE01
          - quantity : 4
          - unitprice : 4.63
          - discount : 0
          - tax : 2.31
          - total : 18.52
          - totalincludingtax : 18.52
          - taxrate : 12.5
          - unitpriceincludingtax : 4.63
          - exchangerate : 0.8
          - hidden\_sellprice : 0
          - description : AIR PRESSURE GAUGE
          - id : 9
          - href
        - }
      - 2
        - {
          - stockcode : ALARM01
          - quantity : 1
          - unitprice : 85.39
          - discount : 0
          - tax : 10.67
          - total : 85.39
          - totalincludingtax : 85.39
          - taxrate : 12.5
          - unitpriceincludingtax : 85.39
          - exchangerate : 0.8
          - hidden\_sellprice : 0
          - description : REMOTE CAR START SECURITY
          - id : 10
          - href
        - }
      - 3
        - {
          - stockcode : FREIGHT
          - quantity : 1
          - unitprice : 8.1
          - discount : 0
          - tax : 1.01
          - total : 8.1
          - totalincludingtax : 8.1
          - taxrate : 12.5
          - unitpriceincludingtax : 8.1
          - exchangerate : 0.8
          - hidden\_sellprice : 0
          - description : FREIGHT
          - id : 11
          - href
        - }
    - }
  - debtorid : 1
  - branchid : 0
  - transactiondate : 2012-10-24T00:00:00+00:00
  - duedate : 2013-01-17T00:00:00+00:00
  - reference1 : 10001
  - reference2 : 83745
  - reference3 : Invoice
  - amount : 277.82
  - allocatedbalance : 277.82
  - outstandingamount : 0
  - transactiontypeid : 1
  - transactiontype : Invoice
  - statusid : 2
  - status : FullyAllocated
  - extrafields
    - {
    - }
  - id : 3
  - href
- }

{URI} is defined as: http://exo.api.myob.com/

|     |     |
| --- | --- |
|  |  |