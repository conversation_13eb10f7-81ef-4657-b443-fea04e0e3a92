---
url: "https://developer.myob.com/api/exo/endpoints/salesorder/salesorderreport/"
title: "Sales Order Report"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Sales Order Report

Return a Sales Order report as a PDF document.

**Date Released:** November 14th 2014 **Date Updated:** November 14th 2014

| URL | Supports |
| --- | --- |
| {URI}/salesorder/{id}/report | [GET](https://developer.myob.com/api/exo/endpoints/salesorder/salesorderreport/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/salesorder/salesorderreport/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/salesorder/salesorderreport/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

The elements list below details information for Sales Order Report. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/salesorder/salesorderreport/#reveal)

#### Attribute Details

This endpoint has no attributes - it returns a Sales Order report for the specified Sales Order. Reports are generated using the **SalesOrd.clf** Clarity form.

#### Example json GET response

|     |     |
| --- | --- |
|  |  |