## Prompt for AI Coding Assistant

Re-create the TeamsysV0.1 project, which is an advanced email processing system. The project should include the following components:

### Core Functionality
- Process all incoming emails, regardless of whether they are orders, general inquiries, or other types of communication.
- Use AI models for intelligent email classification, summarization, and labeling.
- Extract relevant details such as order information, invoices, and purchase orders when applicable.
- Integrate with external APIs (e.g., Gmail, MYOB) for email retrieval and order processing.
- Store extracted data in a structured format (e.g., JSON, database).

### Modules
- **Enhanced Email Processor:** A module for advanced email processing and error handling.
- **PDF Extractor:** Extract data from attached PDFs using OCR or other techniques.
- **MYOB Service:** Handle integration with MYOB for order processing.
- **LLM Service:** Use large language models for intelligent email processing.

### Framework and Workflow
- Use **Pydantic AI** to guide the LLM for structured and validated outputs.
- Implement a **multi-agent asynchronous workflow** for handling emails and MYOB API interactions using provided endpoints.
- Include a **Supervisor module** to delegate tasks based on the email context.

### Memory and Context Management
- Use **Supabase** for LLM memory and vector database for model context management.

### CLI Tool
- Create an interactive CLI for managing the system, including options for email processing, data validation, and analytics.

### Testing
- Include comprehensive unit tests for all modules.
- Provide integration tests for API interactions and data processing workflows.

### Documentation
- Write detailed documentation for setup, usage, and development.
- Include technical analysis and architecture diagrams.

### Configuration
- Use a configuration file for managing environment variables and settings.

### Security
- Implement secure handling of credentials and sensitive data.
- Follow best practices for API authentication and data storage.

### Technologies
- Python for backend development.
- Supabase for LLM memory and vector database management.

### Additional Features
- Parallel processing for handling large volumes of emails efficiently.
- Error handling and logging for debugging and monitoring.
