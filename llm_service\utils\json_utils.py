"""JSON utility functions."""

import json
import logging
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


def safe_json_loads(text: str) -> Optional[Dict[str, Any]]:
    """Safely parse JSON from text, handling common issues."""
    if not text:
        return None
    
    try:
        # Clean the text
        cleaned = text.strip()
        if cleaned.startswith("```json"):
            cleaned = cleaned[7:]
        if cleaned.endswith("```"):
            cleaned = cleaned[:-3]
        cleaned = cleaned.strip()
        
        return json.loads(cleaned)
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"Failed to parse JSON: {e}")
        return None


def sanitize_for_json(obj: Any) -> Any:
    """Recursively sanitize object for JSON serialization."""
    if isinstance(obj, dict):
        return {str(k): sanitize_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [sanitize_for_json(item) for item in obj]
    elif isinstance(obj, (str, int, float, bool)) or obj is None:
        return obj
    else:
        return str(obj)


def format_json_response(data: Dict[str, Any], indent: int = 2) -> str:
    """Format JSON response with consistent indentation."""
    return json.dumps(data, indent=indent, ensure_ascii=False, default=str)