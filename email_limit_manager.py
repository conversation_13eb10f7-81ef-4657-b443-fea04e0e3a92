"""
Dynamic email limit configuration manager.
Handles email processing limits from multiple sources with proper validation.
"""

import os
import logging
from typing import Optional, Dict, Any, Union
from config import config

logger = logging.getLogger(__name__)


class EmailLimitManager:
    """Manages dynamic email limits with multiple configuration sources."""
    
    # Validation constants
    MIN_EMAIL_LIMIT = 1
    MAX_EMAIL_LIMIT = 1000
    DEFAULT_LIMIT = 40
    BRADY_DEFAULT_LIMIT = 100
    
    def __init__(self, runtime_config=None):
        """
        Initialize the email limit manager.
        
        Args:
            runtime_config: Optional runtime configuration object.
        """
        self.runtime_config = runtime_config
        self._cached_limits = {}
    
    def validate_limit(self, limit: Union[int, str]) -> tuple[bool, int, str]:
        """
        Validate an email limit value.
        
        Args:
            limit: Email limit to validate (int or string).
            
        Returns:
            Tuple of (is_valid, validated_limit, error_message).
        """
        try:
            if isinstance(limit, str):
                limit = int(limit)
            
            if not isinstance(limit, int):
                return False, self.DEFAULT_LIMIT, "Limit must be an integer"
            
            if limit < self.MIN_EMAIL_LIMIT:
                return False, self.DEFAULT_LIMIT, f"Limit must be at least {self.MIN_EMAIL_LIMIT}"
            
            if limit > self.MAX_EMAIL_LIMIT:
                return False, self.DEFAULT_LIMIT, f"Limit cannot exceed {self.MAX_EMAIL_LIMIT}"
            
            return True, limit, ""
            
        except (ValueError, TypeError) as e:
            return False, self.DEFAULT_LIMIT, f"Invalid limit value: {e}"
    
    def get_global_override(self) -> Optional[int]:
        """
        Get global email limit override from command-line or environment.
        
        Returns:
            Global override limit or None if not set.
        """
        # Priority 1: Command-line argument
        if self.runtime_config and hasattr(self.runtime_config, 'get_max_results_override'):
            cmd_override = self.runtime_config.get_max_results_override()
            if cmd_override is not None:
                is_valid, validated_limit, error = self.validate_limit(cmd_override)
                if is_valid:
                    logger.info(f"Using global email limit from command-line: {validated_limit}")
                    return validated_limit
                else:
                    logger.warning(f"Invalid command-line email limit {cmd_override}: {error}")
        
        # Priority 2: Environment variable
        env_override = os.getenv("MAX_GMAIL_RESULTS_OVERRIDE")
        if env_override:
            is_valid, validated_limit, error = self.validate_limit(env_override)
            if is_valid:
                logger.info(f"Using global email limit from environment: {validated_limit}")
                return validated_limit
            else:
                logger.warning(f"Invalid environment email limit {env_override}: {error}")
        
        return None
    
    def get_label_specific_limit(self, label_name: str) -> Optional[int]:
        """
        Get label-specific email limit from configuration.
        
        Args:
            label_name: Name of the label.
            
        Returns:
            Label-specific limit or None if not configured.
        """
        try:
            label_settings = config.get_label_settings(label_name)
            limit = label_settings.get('max_results')
            
            if limit is not None:
                is_valid, validated_limit, error = self.validate_limit(limit)
                if is_valid:
                    return validated_limit
                else:
                    logger.warning(f"Invalid label-specific limit for {label_name}: {error}")
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting label-specific limit for {label_name}: {e}")
            return None
    
    def get_per_label_override(self, label_name: str) -> Optional[int]:
        """
        Get per-label email limit override from environment.
        
        Args:
            label_name: Name of the label.
            
        Returns:
            Per-label override limit or None if not set.
        """
        # Check for label-specific environment variable
        env_var = f"MAX_GMAIL_RESULTS_{label_name.upper()}"
        env_value = os.getenv(env_var)
        
        if env_value:
            is_valid, validated_limit, error = self.validate_limit(env_value)
            if is_valid:
                logger.info(f"Using per-label email limit for {label_name} from environment: {validated_limit}")
                return validated_limit
            else:
                logger.warning(f"Invalid per-label environment limit for {label_name}: {error}")
        
        return None
    
    def get_effective_limit(self, label_name: str) -> int:
        """
        Get the effective email limit for a label considering all sources.
        
        Priority order:
        1. Global override (command-line or environment)
        2. Per-label override (environment variable)
        3. Label-specific configuration
        4. Default limits (Brady: 100, others: 10)
        
        Args:
            label_name: Name of the label.
            
        Returns:
            Effective email limit for the label.
        """
        # Use cached value if available
        cache_key = f"{label_name}_{id(self.runtime_config)}"
        if cache_key in self._cached_limits:
            return self._cached_limits[cache_key]
        
        # Priority 1: Global override
        global_override = self.get_global_override()
        if global_override is not None:
            self._cached_limits[cache_key] = global_override
            logger.debug(f"Using global override limit for {label_name}: {global_override}")
            return global_override
        
        # Priority 2: Per-label override
        per_label_override = self.get_per_label_override(label_name)
        if per_label_override is not None:
            self._cached_limits[cache_key] = per_label_override
            logger.debug(f"Using per-label override for {label_name}: {per_label_override}")
            return per_label_override
        
        # Priority 3: Label-specific configuration
        label_specific = self.get_label_specific_limit(label_name)
        if label_specific is not None:
            self._cached_limits[cache_key] = label_specific
            logger.debug(f"Using label-specific limit for {label_name}: {label_specific}")
            return label_specific
        
        # Priority 4: Default limits
        default_limit = self.BRADY_DEFAULT_LIMIT if label_name.lower() == 'brady' else self.DEFAULT_LIMIT
        self._cached_limits[cache_key] = default_limit
        logger.debug(f"Using default limit for {label_name}: {default_limit}")
        return default_limit
    
    def get_limit_source_info(self, label_name: str) -> Dict[str, Any]:
        """
        Get detailed information about the limit source for a label.
        
        Args:
            label_name: Name of the label.
            
        Returns:
            Dictionary containing limit source information.
        """
        effective_limit = self.get_effective_limit(label_name)
        
        # Determine the source
        global_override = self.get_global_override()
        per_label_override = self.get_per_label_override(label_name)
        label_specific = self.get_label_specific_limit(label_name)
        
        if global_override is not None:
            source = "global_override"
            source_detail = "Command-line or environment variable"
        elif per_label_override is not None:
            source = "per_label_override"
            source_detail = f"Environment variable MAX_GMAIL_RESULTS_{label_name.upper()}"
        elif label_specific is not None:
            source = "label_specific"
            source_detail = "Label-specific configuration"
        else:
            source = "default"
            source_detail = "Default configuration"
        
        return {
            'label_name': label_name,
            'effective_limit': effective_limit,
            'source': source,
            'source_detail': source_detail,
            'global_override': global_override,
            'per_label_override': per_label_override,
            'label_specific': label_specific,
            'is_brady_default': label_name.lower() == 'brady' and source == 'default'
        }
    
    def get_all_limits_summary(self, label_names: list) -> Dict[str, Dict[str, Any]]:
        """
        Get a summary of limits for all specified labels.
        
        Args:
            label_names: List of label names.
            
        Returns:
            Dictionary mapping label names to their limit information.
        """
        summary = {}
        for label_name in label_names:
            summary[label_name] = self.get_limit_source_info(label_name)
        return summary
    
    def clear_cache(self):
        """Clear the cached limits."""
        self._cached_limits.clear()
        logger.debug("Cleared email limit cache")
    
    def log_limits_summary(self, label_names: list):
        """
        Log a summary of email limits for the specified labels.
        
        Args:
            label_names: List of label names to summarize.
        """
        logger.info("📊 Email Limit Configuration Summary:")
        logger.info("=" * 50)
        
        summary = self.get_all_limits_summary(label_names)
        
        for label_name, info in summary.items():
            limit = info['effective_limit']
            source = info['source_detail']
            logger.info(f"📧 {label_name}: {limit} emails ({source})")
        
        # Show global settings if any
        global_override = self.get_global_override()
        if global_override:
            logger.info(f"🌍 Global override active: {global_override} emails")
        
        logger.info("=" * 50)


def get_email_limit_manager(runtime_config=None) -> EmailLimitManager:
    """
    Get an email limit manager instance.
    
    Args:
        runtime_config: Optional runtime configuration object.
        
    Returns:
        EmailLimitManager instance.
    """
    return EmailLimitManager(runtime_config)


if __name__ == "__main__":
    # Test the email limit manager
    print("🧪 Testing Email Limit Manager...")
    
    manager = EmailLimitManager()
    
    # Test validation
    valid, limit, error = manager.validate_limit(50)
    print(f"Validate 50: {valid}, {limit}, {error}")
    
    valid, limit, error = manager.validate_limit(2000)
    print(f"Validate 2000: {valid}, {limit}, {error}")
    
    # Test effective limits
    brady_limit = manager.get_effective_limit('Brady')
    rsea_limit = manager.get_effective_limit('RSEA')
    print(f"Brady limit: {brady_limit}")
    print(f"RSEA limit: {rsea_limit}")
    
    # Test summary
    summary = manager.get_all_limits_summary(['Brady', 'RSEA', 'Unknown'])
    for label, info in summary.items():
        print(f"{label}: {info['effective_limit']} ({info['source']})")
    
    print("✅ Email Limit Manager working correctly!")
