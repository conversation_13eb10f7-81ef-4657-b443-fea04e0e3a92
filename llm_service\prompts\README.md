# Centralized Prompt Management System

## Overview

The centralized prompt management system provides a unified way for all agents to access, use, and maintain prompts. This system ensures consistency, reduces duplication, and makes it easy to update business rules across all agents.

## Architecture

```
llm_service/prompts/
├── __init__.py              # Package initialization
├── base_prompts.py          # Core prompt components
├── order_prompts.py         # Order processing prompts
├── summary_prompts.py       # Email summary prompts
├── system_prompts.py        # System-level prompts
├── prompt_manager.py        # Central management class
└── README.md               # This documentation
```

## Key Components

### 1. PromptComponent
Individual prompt components that can be combined and reused:
```python
@dataclass
class PromptComponent:
    name: str           # Unique identifier
    content: str        # Prompt text
    category: str       # Grouping category
    priority: int = 0   # Priority for ordering
```

### 2. BasePrompts
Core prompt components used across all agents:
- `SYSTEM_IDENTITY` - Team Systems identity and capabilities
- `JSON_ENFORCEMENT` - Critical JSON output instructions
- `COMPANY_CONTEXT` - Business context and rules
- `EMAIL_RULES` - Email processing guidelines
- `SKU_RULES` - Product/SKU handling rules

### 3. OrderPrompts
Specialized prompts for order processing:
- `ORDER_EXTRACTION_SYSTEM` - Order extraction system prompt
- `DEBTOR_RULES` - Customer identification rules
- `SHIPPING_RULES` - Dispatch and shipping logic
- `ORDER_LINE_RULES` - Line item processing
- `ORDER_JSON_TEMPLATE` - Required JSON structure

### 4. SummaryPrompts
Email analysis and summary prompts:
- `EMAIL_ANALYSIS_SYSTEM` - Email analysis system prompt
- `INTENT_RECOGNITION` - Email intent classification
- `SUMMARY_STRUCTURE` - Summary formatting rules
- `MARKDOWN_RULES` - Markdown generation guidelines

### 5. SystemPrompts
System-level prompts for MistralAI:
- `MISTRAL_SYSTEM_PROMPT` - Core system prompt
- `JSON_MODE_ENFORCEMENT` - Strict JSON enforcement
- `FALLBACK_PROMPT_ENHANCEMENT` - Non-JSON mode fallback
- `ERROR_RECOVERY_PROMPT` - Error recovery handling

## Usage Examples

### Basic Usage
```python
from llm_service.prompts.prompt_manager import prompt_manager

# Get order extraction prompt
prompt = prompt_manager.get_order_extraction_prompt(content, context)

# Get email summary prompt
summary_prompt = prompt_manager.get_email_summary_prompt(email_body, subject, sender)

# Get system prompt for MistralAI
system_prompt = prompt_manager.get_mistral_system_prompt()
```

### Advanced Usage
```python
# List all available prompts
available_prompts = prompt_manager.list_available_prompts()

# Get prompt information
info = prompt_manager.get_prompt_info('order_extraction')

# Get business rules section
debtor_rules = prompt_manager.get_business_rules_section('debtors')

# Export all prompts for analysis
prompt_manager.export_prompts_to_json('prompts_backup.json')
```

### Agent Integration
```python
class MyAgent:
    def __init__(self):
        self.prompt_manager = prompt_manager
    
    async def process_order(self, content: str, context: str = ""):
        # Use centralized prompt
        prompt = self.prompt_manager.get_order_extraction_prompt(content, context)
        
        # Process with LLM
        response = await self.llm_service.generate_content(prompt)
        return response
```

## Business Rules Integration

The system automatically integrates business rules from `templates/rules.txt`:

### Rules Structure
```
**System:**
- Core system capabilities and identity

**Company:**
- Business context and operations

**Colleagues:**
- Internal vs external communication rules

**Emails:**
- Email processing guidelines

**Debtors (Customers):**
- Customer-specific handling rules

**Products (SKU):**
- Product and SKU processing rules

**Misc:**
- Additional business logic
```

### Automatic Integration
Business rules are automatically loaded and integrated into prompts:
```python
# Rules are automatically included in order extraction prompts
prompt = prompt_manager.get_order_extraction_prompt(content)
# This includes all relevant business rules from templates/rules.txt
```

## Available Prompts

| Prompt Name | Description | Parameters | Category |
|-------------|-------------|------------|----------|
| `order_extraction` | Extract order data from content | content, context | order_processing |
| `order_validation` | Validate extracted order data | data | order_processing |
| `email_summary` | Generate email summary and intent | email_body, subject, sender | email_analysis |
| `markdown_summary` | Generate markdown summary | email_body, subject, sender, pdf_content | email_analysis |
| `intent_classification` | Classify email intent | email_content | email_analysis |
| `mistral_system` | System prompt for MistralAI | none | system |
| `json_enforcement` | JSON enforcement prompt | none | system |

## Extending the System

### Adding New Prompts
1. Create prompt components in appropriate class:
```python
class MyPrompts(BasePrompts):
    NEW_PROMPT = PromptComponent(
        name="new_prompt",
        content="Your prompt content here",
        category="my_category",
        priority=5
    )
```

2. Add method to PromptManager:
```python
def get_my_new_prompt(self, param1: str) -> str:
    return f"Enhanced prompt with {param1}"
```

### Adding New Categories
1. Create new prompt class inheriting from BasePrompts
2. Add methods to PromptManager
3. Update available prompts list
4. Add documentation

## Best Practices

### 1. Prompt Design
- Use clear, specific instructions
- Include business context
- Specify output format explicitly
- Add error handling instructions

### 2. Component Reuse
- Use existing components when possible
- Create reusable components for common patterns
- Maintain consistent naming conventions

### 3. Business Rules
- Keep business rules in `templates/rules.txt`
- Use specific sections for different rule types
- Update rules centrally to affect all agents

### 4. Testing
- Test prompts with various input types
- Validate JSON output structure
- Check business rule integration

### 5. Maintenance
- Regular review of prompt effectiveness
- Update based on production feedback
- Version control prompt changes

## Integration with Agents

### Order Processing Agent
```python
class OrderProcessor:
    def __init__(self, mistral_service, memory_service):
        self.mistral_service = mistral_service
        self.memory_service = memory_service
    
    async def extract_order_data(self, content: str) -> Optional[Dict[str, Any]]:
        # Get context from memory
        context = self.memory_service.get_relevant_context(content[:500])
        
        # Use centralized prompt
        prompt = prompt_manager.get_order_extraction_prompt(content, context)
        
        # Process with enhanced Mistral service
        response = await self.mistral_service.generate_content(prompt)
        return json.loads(response.text)
```

### Summary Processing Agent
```python
class SummaryProcessor:
    async def generate_email_summary(self, email_body: str, subject: str) -> EmailSummary:
        # Use centralized prompt
        prompt = prompt_manager.get_email_summary_prompt(email_body, subject)
        
        response = await self.mistral_service.generate_content(prompt)
        return EmailSummary(**json.loads(response.text))
```

## Benefits

### 1. Consistency
- All agents use the same business rules
- Consistent prompt structure and formatting
- Unified error handling and recovery

### 2. Maintainability
- Single source of truth for business rules
- Easy to update prompts across all agents
- Centralized testing and validation

### 3. Scalability
- Easy to add new agents and prompts
- Reusable components reduce development time
- Consistent integration patterns

### 4. Quality
- Enhanced JSON processing with multiple fallback strategies
- Business rule integration ensures compliance
- Comprehensive error handling and recovery

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```python
   # Correct import
   from llm_service.prompts.prompt_manager import prompt_manager
   ```

2. **Missing Business Rules**
   - Ensure `templates/rules.txt` exists
   - Check file permissions and encoding
   - Verify rule section formatting

3. **Prompt Not Found**
   ```python
   # Check available prompts
   available = prompt_manager.list_available_prompts()
   print(available)
   ```

4. **JSON Parsing Errors**
   - Use JSON enforcement prompts
   - Check response cleaning logic
   - Verify MistralAI JSON mode configuration

### Debug Mode
```python
# Export prompts for analysis
prompt_manager.export_prompts_to_json('debug_prompts.json')

# Get detailed prompt info
info = prompt_manager.get_prompt_info('order_extraction')
print(info)
```

This centralized system ensures all agents can efficiently access and use consistent, high-quality prompts while maintaining business rule compliance and providing robust error handling.