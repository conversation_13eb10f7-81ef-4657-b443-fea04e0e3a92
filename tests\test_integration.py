"""Integration tests for LLM service."""

import pytest
from llm_service.main import LLMService, LegacyLLMService


class TestLLMServiceIntegration:
    """Integration tests for the refactored LLM service."""
    
    @pytest.mark.asyncio
    async def test_full_email_processing(self):
        """Test complete email processing flow."""
        service = LLMService(mistral_api_key="test-key")
        
        email_body = """
        Purchase Order #PO-12345
        
        Customer: TEST COMPANY
        Please supply:
        - ABC123: 5 units
        - DEF456: 3 units
        
        Deliver to: 123 Test Street, Test City
        """
        
        result = await service.process_email(
            email_body=email_body,
            subject="Purchase Order PO-12345",
            sender="<EMAIL>"
        )
        
        assert "summary" in result
        assert "order" in result
        assert "myob_payload" in result
    
    @pytest.mark.asyncio
    async def test_legacy_compatibility(self):
        """Test legacy compatibility wrapper."""
        service = LegacyLLMService(mistral_api_key="test-key")
        
        email_body = "Test email content"
        
        summary = await service.generate_email_summary(email_body, "Test Subject")
        assert "summary" in summary
        assert "action_required" in summary


class TestZeroDowntimeMigration:
    """Tests for zero-downtime migration."""
    
    def test_legacy_api_compatibility(self):
        """Test that legacy API calls still work."""
        # Ensure backward compatibility
        pass
    
    def test_new_api_functionality(self):
        """Test new API functionality."""
        # Test new async features
        pass