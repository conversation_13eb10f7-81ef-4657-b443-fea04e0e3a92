#!/usr/bin/env python3
"""
Debug MYOB data to understand the field mappings and relationships.
"""
import json
from myob_service import MyobService

def main():
    myob = MyobService()
    
    print("=== DEBUGGING MYOB DATA ===")
    
    # Get sales orders
    print("\n1. Sales Orders:")
    orders = myob.get_all_sales_orders()
    if orders:
        print(f"Found {len(orders)} sales orders")
        print("First sales order:")
        print(json.dumps(orders[0], indent=2))
        
        # Extract debtor IDs from sales orders
        debtor_ids_from_orders = set()
        for order in orders:
            if 'debtorid' in order:
                debtor_ids_from_orders.add(order['debtorid'])
        
        print(f"\nUnique debtor IDs from sales orders: {sorted(debtor_ids_from_orders)}")
    
    # Get debtors
    print("\n2. Debtors:")
    debtors = myob.get_all_debtors()
    if debtors:
        print(f"Found {len(debtors)} debtors")
        print("First debtor:")
        print(json.dumps(debtors[0], indent=2))
        
        # Extract IDs from debtors
        debtor_ids_from_debtors = set()
        for debtor in debtors:
            if 'id' in debtor:
                debtor_ids_from_debtors.add(debtor['id'])
        
        print(f"\nUnique IDs from debtors: {sorted(debtor_ids_from_debtors)}")
    
    # Check overlap
    if orders and debtors:
        print(f"\n3. ID Overlap Analysis:")
        print(f"Debtor IDs from orders: {sorted(debtor_ids_from_orders)}")
        print(f"IDs from debtors: {sorted(debtor_ids_from_debtors)}")
        
        overlap = debtor_ids_from_orders.intersection(debtor_ids_from_debtors)
        print(f"Overlapping IDs: {sorted(overlap)}")
        print(f"Orders with no matching debtor: {sorted(debtor_ids_from_orders - debtor_ids_from_debtors)}")
        print(f"Debtors with no orders: {sorted(debtor_ids_from_debtors - debtor_ids_from_orders)}")
    
    # Test search for Woolworths
    print(f"\n4. Woolworths Search:")
    woolworths_results = myob.search_debtor("woolworth")
    if woolworths_results:
        print(f"Found {len(woolworths_results)} Woolworths-related debtors:")
        for i, debtor in enumerate(woolworths_results):
            print(f"  {i+1}. ID: {debtor.get('id')}, Name: {debtor.get('accountname')}")

if __name__ == "__main__":
    main()