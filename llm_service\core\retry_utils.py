"""
Retry utilities for handling network failures and improving system resilience.
"""

import logging
import time
from functools import wraps
from typing import Callable, Any, Tuple, Type
from tenacity import (
    retry, 
    stop_after_attempt, 
    wait_exponential, 
    retry_if_exception_type,
    before_sleep_log,
    after_log
)
import socket
import requests

logger = logging.getLogger(__name__)

# Common network-related exceptions that should trigger retries
NETWORK_EXCEPTIONS = (
    socket.gaierror,  # DNS resolution failures
    socket.timeout,
    ConnectionError,
    requests.exceptions.ConnectionError,
    requests.exceptions.Timeout,
    requests.exceptions.RequestException,
    OSError,  # General OS-level network errors
)

def network_retry(
    max_attempts: int = 3,
    min_wait: int = 2,
    max_wait: int = 10,
    exponential_base: int = 2
):
    """
    Decorator for retrying network operations with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        min_wait: Minimum wait time between retries (seconds)
        max_wait: Maximum wait time between retries (seconds)
        exponential_base: Base for exponential backoff
    """
    return retry(
        stop=stop_after_attempt(max_attempts),
        wait=wait_exponential(
            multiplier=1,
            min=min_wait,
            max=max_wait,
            exp_base=exponential_base
        ),
        retry=retry_if_exception_type(NETWORK_EXCEPTIONS),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        after=after_log(logger, logging.INFO)
    )

def api_retry(
    max_attempts: int = 3,
    min_wait: int = 1,
    max_wait: int = 8
):
    """
    Decorator specifically for API calls with shorter wait times.
    """
    return retry(
        stop=stop_after_attempt(max_attempts),
        wait=wait_exponential(
            multiplier=1,
            min=min_wait,
            max=max_wait
        ),
        retry=retry_if_exception_type(NETWORK_EXCEPTIONS + (Exception,)),
        before_sleep=before_sleep_log(logger, logging.WARNING),
        after=after_log(logger, logging.INFO)
    )

def dns_retry(max_attempts: int = 5, wait_time: int = 2):
    """
    Decorator specifically for DNS-related operations.
    """
    return retry(
        stop=stop_after_attempt(max_attempts),
        wait=wait_exponential(multiplier=1, min=wait_time, max=30),
        retry=retry_if_exception_type((socket.gaierror, OSError)),
        before_sleep=before_sleep_log(logger, logging.ERROR),
        after=after_log(logger, logging.INFO)
    )

class CircuitBreaker:
    """
    Simple circuit breaker pattern implementation for handling repeated failures.
    """
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection.
        """
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                logger.info("Circuit breaker moving to HALF_OPEN state")
            else:
                raise Exception("Circuit breaker is OPEN - service unavailable")
        
        try:
            result = func(*args, **kwargs)
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
                logger.info("Circuit breaker reset to CLOSED state")
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
                logger.error(f"Circuit breaker opened after {self.failure_count} failures")
            
            raise e

def with_circuit_breaker(failure_threshold: int = 5, recovery_timeout: int = 60):
    """
    Decorator to add circuit breaker functionality to a function.
    """
    circuit_breaker = CircuitBreaker(failure_threshold, recovery_timeout)
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            return circuit_breaker.call(func, *args, **kwargs)
        return wrapper
    return decorator

def check_network_connectivity(host: str = "*******", port: int = 53, timeout: int = 3) -> bool:
    """
    Check basic network connectivity by attempting to connect to a reliable host.
    
    Args:
        host: Host to test connectivity (default: Google DNS)
        port: Port to test (default: DNS port 53)
        timeout: Connection timeout in seconds
    
    Returns:
        True if connection successful, False otherwise
    """
    try:
        socket.setdefaulttimeout(timeout)
        socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect((host, port))
        return True
    except Exception:
        return False

def wait_for_network(max_wait: int = 60, check_interval: int = 5) -> bool:
    """
    Wait for network connectivity to be restored.
    
    Args:
        max_wait: Maximum time to wait in seconds
        check_interval: How often to check connectivity in seconds
    
    Returns:
        True if network is available, False if timeout reached
    """
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        if check_network_connectivity():
            logger.info("Network connectivity restored")
            return True
        
        logger.warning(f"Network unavailable, retrying in {check_interval} seconds...")
        time.sleep(check_interval)
    
    logger.error(f"Network connectivity not restored after {max_wait} seconds")
    return False