#!/usr/bin/env python3
"""Quick script to check available customers in the database."""

from supabase_database_service import SupabaseService

def main():
    db = SupabaseService()
    customers = db.get_all_customers()
    
    print("Available customers:")
    print("-" * 50)
    
    for customer in customers[:20]:  # Show first 20
        print(f"ID: {customer['debtor_id']:>6} | Name: {customer['customer_name']}")
    
    print(f"\nTotal customers: {len(customers)}")
    
    # Check for Woolworths specifically (more comprehensive search)
    woolworths_customers = [c for c in customers if any(term in c['customer_name'].lower() for term in ['woolworth', 'woolies', 'alh group'])]
    if woolworths_customers:
        print(f"\nWoolworths-related customers ({len(woolworths_customers)} found):")
        print("-" * 60)
        for customer in woolworths_customers:
            print(f"ID: {customer['debtor_id']:>6} | Name: {customer['customer_name']}")
    else:
        print("\nNo Woolworths-related customers found")
    
    # Also check for other common retail chains for reference
    print("\nOther major retail chains:")
    print("-" * 40)
    retail_chains = ['coles', 'iga', 'foodland', 'drakes', 'metcash']
    for chain in retail_chains:
        chain_customers = [c for c in customers if chain in c['customer_name'].lower()]
        if chain_customers:
            print(f"{chain.upper()}: {len(chain_customers)} customers")
            for customer in chain_customers[:3]:  # Show first 3
                print(f"  ID: {customer['debtor_id']:>6} | Name: {customer['customer_name']}")
            if len(chain_customers) > 3:
                print(f"  ... and {len(chain_customers) - 3} more")

if __name__ == "__main__":
    main()