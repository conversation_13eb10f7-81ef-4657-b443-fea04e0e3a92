# Requirements for TeamsysV0.1 with Supabase Memory Integration
# Core dependencies
python-dotenv>=1.0.0
pydantic>=2.0.0
typing-extensions>=4.0.0

# Gmail API
google-auth>=2.0.0
google-auth-oauthlib>=1.0.0
google-api-python-client>=2.0.0

# Google Gemini LLM
google-generativeai>=0.3.0

# Mistral AI API
mistralai>=1.0.0

# Supabase client
supabase>=2.0.0
postgrest>=0.10.0

# PDF processing
PyPDF2>=3.0.0
pdfplumber>=0.7.0

# Email processing
email-validator>=2.0.0
beautifulsoup4>=4.11.0

# Template engine
jinja2>=3.0.0

# HTTP requests
requests>=2.28.0

# Web API
fastapi>=0.104.0
uvicorn>=0.24.0

# Logging
colorlog>=6.0.0

# Scheduling (if needed)
schedule>=1.2.0

# Data processing
pandas>=1.5.0

# Fuzzy string matching
fuzzywuzzy>=0.18.0
python-Levenshtein>=0.20.0  # For faster fuzzy matching

# JSON handling
orjson>=3.8.0  # Optional, faster JSON processing

# ChromaDB (for backward compatibility if needed)
chromadb>=0.4.0

# Vector embeddings (optional)
sentence-transformers>=2.2.0

# Testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
