#!/usr/bin/env python3
"""
Upload active debtors from Excel file to Supabase, replacing the existing customers table.
"""
import pandas as pd
import logging
from datetime import datetime
from typing import List, Dict, Any
from supabase_database_service import SupabaseService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_active_debtors(file_path: str) -> List[Dict[str, Any]]:
    """Load active debtors from Excel file and prepare for Supabase."""
    try:
        logger.info(f"Loading active debtors from: {file_path}")
        
        # Read Excel file
        df = pd.read_excel(file_path)
        logger.info(f"Loaded {len(df)} active debtors")
        
        # Prepare records for Supabase
        records = []
        for _, row in df.iterrows():
            record = {
                'debtor_id': int(row['debtor_id']),
                'customer_name': str(row['account_name']).strip(),
                'email': None,
                'phone': None,
                'address_line1': None,
                'address_line2': None,
                'address_line3': None,
                'address_line4': None,
                'contact_person': None,
                'payment_terms': None,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            records.append(record)
        
        logger.info(f"Prepared {len(records)} records for upload")
        return records
        
    except Exception as e:
        logger.error(f"Error loading active debtors: {e}")
        return []

def clear_existing_customers(db: SupabaseService) -> bool:
    """Completely clear the customers table by handling foreign key constraints."""
    try:
        logger.info("Clearing existing customers table...")
        
        # Get count of existing customers
        existing = db.supabase.table("customers").select("debtor_id", count="exact").execute()
        existing_count = existing.count or 0
        logger.info(f"Found {existing_count} existing customers")
        
        if existing_count == 0:
            logger.info("No existing customers to clear")
            return True
        
        # Step 1: Clear dependent tables first to avoid foreign key constraints
        logger.info("Clearing dependent records in correct order...")
        
        # Clear order_line_items first (references erp_payloads)
        try:
            line_items_result = db.supabase.table("order_line_items").delete().neq("id", "00000000-0000-0000-0000-000000000000").execute()
            logger.info(f"Cleared order_line_items table")
        except Exception as e:
            logger.warning(f"Could not clear order_line_items: {e}")
        
        # Clear erp_payloads (references customers via debtor_id)
        try:
            payloads_result = db.supabase.table("erp_payloads").delete().neq("id", "00000000-0000-0000-0000-000000000000").execute()
            logger.info(f"Cleared erp_payloads table")
        except Exception as e:
            logger.warning(f"Could not clear erp_payloads: {e}")
        
        # Clear email_events (references customers via debtor_id)
        try:
            events_result = db.supabase.table("email_events").delete().neq("id", "00000000-0000-0000-0000-000000000000").execute()
            logger.info(f"Cleared email_events table")
        except Exception as e:
            logger.warning(f"Could not clear email_events: {e}")
        
        # Clear email_categorizations (might reference emails)
        try:
            categorizations_result = db.supabase.table("email_categorizations").delete().neq("id", "00000000-0000-0000-0000-000000000000").execute()
            logger.info(f"Cleared email_categorizations table")
        except Exception as e:
            logger.warning(f"Could not clear email_categorizations: {e}")
        
        # Step 2: Now clear the customers table
        logger.info("Clearing customers table...")
        customers_result = db.supabase.table("customers").delete().neq("debtor_id", 0).execute()
        logger.info(f"Successfully cleared customers table")
        
        # Verify clearing
        remaining = db.supabase.table("customers").select("debtor_id", count="exact").execute()
        remaining_count = remaining.count or 0
        logger.info(f"Remaining customers after clearing: {remaining_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error clearing customers table: {e}")
        return False

def upload_active_debtors(db: SupabaseService, records: List[Dict[str, Any]]) -> bool:
    """Upload active debtors to Supabase in batches."""
    try:
        logger.info(f"Uploading {len(records)} active debtors to Supabase...")
        
        # Upload in batches of 100 to avoid API limits
        batch_size = 100
        total_uploaded = 0
        
        for i in range(0, len(records), batch_size):
            batch = records[i:i + batch_size]
            
            try:
                result = db.supabase.table("customers").insert(batch).execute()
                if result.data:
                    total_uploaded += len(batch)
                    logger.info(f"Uploaded batch {i//batch_size + 1}: {len(batch)} records (Total: {total_uploaded})")
                else:
                    logger.error(f"Failed to upload batch {i//batch_size + 1}")
                    
            except Exception as e:
                logger.error(f"Error uploading batch {i//batch_size + 1}: {e}")
                continue
        
        logger.info(f"Successfully uploaded {total_uploaded} active debtors")
        return total_uploaded == len(records)
        
    except Exception as e:
        logger.error(f"Error uploading active debtors: {e}")
        return False

def verify_upload(db: SupabaseService) -> Dict[str, Any]:
    """Verify the upload was successful."""
    try:
        logger.info("Verifying upload...")
        
        # Get total count
        result = db.supabase.table("customers").select("debtor_id", count="exact").execute()
        total_count = result.count or 0
        
        # Check for Woolworths entries
        woolworths_result = db.supabase.table("customers").select("*").ilike("customer_name", "%woolworth%").execute()
        woolworths_count = len(woolworths_result.data) if woolworths_result.data else 0
        
        # Test customer lookup
        test_customer = db.find_customer_by_name("Woolworths")
        
        verification = {
            'total_customers': total_count,
            'woolworths_entries': woolworths_count,
            'woolworths_lookup_success': test_customer is not None,
            'woolworths_lookup_result': test_customer
        }
        
        logger.info(f"Verification results:")
        logger.info(f"  Total customers: {total_count}")
        logger.info(f"  Woolworths entries: {woolworths_count}")
        logger.info(f"  Woolworths lookup: {'✅ Success' if test_customer else '❌ Failed'}")
        
        if test_customer:
            logger.info(f"  Found: ID {test_customer['debtor_id']} - {test_customer['customer_name']}")
        
        return verification
        
    except Exception as e:
        logger.error(f"Error verifying upload: {e}")
        return {'error': str(e)}

def main():
    """Main function to upload active debtors."""
    file_path = r"C:\Users\<USER>\Desktop\active_debtors.xlsx"
    
    print("ACTIVE DEBTORS UPLOAD TO SUPABASE")
    print("=" * 60)
    
    # Load active debtors from Excel
    records = load_active_debtors(file_path)
    if not records:
        print("❌ Failed to load active debtors")
        return
    
    # Initialize Supabase service
    db = SupabaseService()
    
    # Clear existing customers and dependent records
    print("\n🗑️  Clearing existing customers table (complete replacement)...")
    if not clear_existing_customers(db):
        print("❌ Failed to clear existing customers")
        return
    
    # Upload active debtors
    print("\n📤 Uploading active debtors...")
    if not upload_active_debtors(db, records):
        print("❌ Failed to upload active debtors")
        return
    
    # Verify upload
    print("\n✅ Verifying upload...")
    verification = verify_upload(db)
    
    print(f"\n" + "=" * 60)
    print("UPLOAD COMPLETE")
    print("=" * 60)
    print(f"✅ Successfully uploaded {verification.get('total_customers', 0)} active customers")
    print(f"✅ Found {verification.get('woolworths_entries', 0)} Woolworths-related entries")
    
    if verification.get('woolworths_lookup_success'):
        result = verification['woolworths_lookup_result']
        print(f"✅ Woolworths lookup test: ID {result['debtor_id']} - {result['customer_name']}")
    else:
        print("❌ Woolworths lookup test failed")
    
    print(f"\n🎯 Ready to test with: python main_processor.py --label Woolworths --max-emails 1")

if __name__ == "__main__":
    main()