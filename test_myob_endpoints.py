#!/usr/bin/env python3
"""
Test MYOB API endpoints to find the correct ones for getting debtor data.
"""
import requests
import json
import logging
from config import config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_myob_endpoint(endpoint_path, params=None):
    """Test a MYOB API endpoint."""
    url = f"{config.MYOB_BASE_URL}{endpoint_path}"
    logger.info(f"Testing endpoint: {url}")
    
    try:
        response = requests.get(
            url,
            headers=config.MYOB_HEADERS,
            params=params,
            timeout=30
        )
        
        logger.info(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Success! Retrieved {len(data) if isinstance(data, list) else 'data'}")
            if isinstance(data, list) and len(data) > 0:
                logger.info(f"Sample record keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'N/A'}")
            return data
        else:
            logger.error(f"Error: {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"Exception: {e}")
        return None

def main():
    """Test various MYOB endpoints to find working ones."""
    
    print("Testing MYOB API Endpoints")
    print("=" * 50)
    
    # Test basic endpoints first
    endpoints_to_test = [
        "/debtor",
        "/debtors", 
        "/customer",
        "/customers",
        "/salesorder",
        "/stock_item",
        "/report"
    ]
    
    working_endpoints = []
    
    for endpoint in endpoints_to_test:
        print(f"\n--- Testing {endpoint} ---")
        result = test_myob_endpoint(endpoint)
        if result is not None:
            working_endpoints.append(endpoint)
            # Limit output for large datasets
            if isinstance(result, list) and len(result) > 3:
                print(f"First 3 records:")
                for i, record in enumerate(result[:3]):
                    print(f"  {i+1}: {json.dumps(record, indent=2)[:200]}...")
            else:
                print(f"Result: {json.dumps(result, indent=2)[:500]}...")
    
    print(f"\n{'='*50}")
    print(f"Working endpoints: {working_endpoints}")
    
    # If debtor endpoint works, try to get a specific debtor report
    if "/debtor" in working_endpoints:
        print(f"\n--- Testing debtor-specific endpoints ---")
        
        # Try to get first debtor ID
        debtors = test_myob_endpoint("/debtor")
        if debtors and len(debtors) > 0:
            first_debtor_id = debtors[0].get('debtorid') or debtors[0].get('id')
            if first_debtor_id:
                print(f"Testing report for debtor ID: {first_debtor_id}")
                test_myob_endpoint(f"/debtor/{first_debtor_id}/report")
                
                # Try search functionality
                print(f"Testing debtor search...")
                test_myob_endpoint("/debtor/search", params={'q': 'woolworth'})

if __name__ == "__main__":
    main()