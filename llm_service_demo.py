#!/usr/bin/env python3
"""
Comprehensive Demo of the LLM Service
This script demonstrates all the capabilities of the refactored LLM service.
"""

import asyncio
import logging
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the LLM service
from llm_service import LLMService

# Configure logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce log noise for demo
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_section(title):
    """Print a formatted section header."""
    print(f"\n{'-'*40}")
    print(f"  {title}")
    print(f"{'-'*40}")

async def demo_email_processing():
    """Demonstrate email processing capabilities."""
    print_header("🚀 LLM SERVICE COMPREHENSIVE DEMO")
    
    async with LLMService() as llm_service:
        print("✅ LLM Service initialized successfully")
        
        # Health check
        health = await llm_service.health_check()
        print(f"🏥 Health Status: Mistral={health.get('mistral', False)}, Memory={health.get('memory', False)}")
        
        if not health.get('mistral', False):
            print("❌ Mistral service is not healthy. Please check your API key.")
            return
        
        # Demo 1: Simple Purchase Order
        print_section("📧 Demo 1: Simple Purchase Order")
        
        simple_po = """
        Subject: Purchase Order PO-2025-100
        From: <EMAIL>
        
        Hi Team,
        
        Please process this purchase order:
        
        Customer: ACME Corporation
        PO Number: PO-2025-100
        
        Items:
        1. Office Chairs (Model: EC-200) - Qty: 25 @ $150.00 each
        2. Desk Lamps (Model: DL-50) - Qty: 10 @ $45.00 each
        3. Filing Cabinets (Model: FC-4D) - Qty: 5 @ $280.00 each
        
        Delivery Address:
        ACME Corporation
        456 Business Ave
        Melbourne VIC 3000
        Australia
        
        Total: $5,600.00
        Payment Terms: Net 30
        
        Please confirm receipt and expected delivery date.
        
        Best regards,
        Sarah Johnson
        Procurement Manager
        """
        
        result1 = await llm_service.process_email(
            email_body=simple_po,
            subject="Purchase Order PO-2025-100",
            sender="<EMAIL>",
            email_id="demo-001"
        )
        
        print("📊 Results:")
        print(f"  Summary: {result1.get('summary', {}).get('summary', 'N/A')}")
        print(f"  Order Found: {'✅ Yes' if result1.get('order') else '❌ No'}")
        
        if result1.get('order'):
            order = result1['order']
            print(f"  Customer: {order.get('customer_details', {}).get('customer_name', 'N/A')}")
            print(f"  PO Number: {order.get('purchase_order_number', 'N/A')}")
            print(f"  Line Items: {len(order.get('line_items', []))}")
            print(f"  Total: ${order.get('total_amount', 'N/A')} {order.get('currency', 'AUD')}")
            
            # Show line items
            for i, item in enumerate(order.get('line_items', [])[:3], 1):
                print(f"    {i}. {item.get('description', 'N/A')} - Qty: {item.get('quantity', 'N/A')} @ ${item.get('unit_price', 'N/A')}")
        
        # Demo 2: Complex Order with Special Instructions
        print_section("📧 Demo 2: Complex Order with Special Instructions")
        
        complex_order = """
        Subject: URGENT: Rush Order for Event Setup
        From: <EMAIL>
        
        URGENT RUSH ORDER REQUIRED
        
        Customer: MegaCorp Events Division
        Reference: EVENT-2025-SUMMER
        
        We need the following items for our summer conference:
        
        FURNITURE:
        - Round Tables (8-seater): 15 units @ $120.00 each
        - Cocktail Tables (standing): 8 units @ $85.00 each
        - Executive Chairs: 120 units @ $95.00 each
        
        AV EQUIPMENT:
        - Wireless Microphones: 6 units @ $250.00 each
        - Projection Screens (10ft): 3 units @ $450.00 each
        - Sound Systems (portable): 2 units @ $800.00 each
        
        SPECIAL INSTRUCTIONS:
        - RUSH DELIVERY REQUIRED by July 25th, 2025
        - Setup service required on-site
        - All items must be in pristine condition
        - Backup equipment recommended for critical items
        
        Delivery Location:
        Melbourne Convention Centre
        1 Convention Centre Place
        South Wharf VIC 3006
        Australia
        
        Contact: James Wilson (0412 345 678)
        
        Total Estimated: $25,000.00 (excluding setup fees)
        
        Please confirm availability and rush delivery surcharge.
        
        Thanks,
        Lisa Chen
        Event Coordinator
        """
        
        result2 = await llm_service.process_email(
            email_body=complex_order,
            subject="URGENT: Rush Order for Event Setup",
            sender="<EMAIL>",
            email_id="demo-002"
        )
        
        print("📊 Results:")
        print(f"  Summary: {result2.get('summary', {}).get('summary', 'N/A')}")
        print(f"  Order Found: {'✅ Yes' if result2.get('order') else '❌ No'}")
        
        if result2.get('order'):
            order = result2['order']
            print(f"  Customer: {order.get('customer_details', {}).get('customer_name', 'N/A')}")
            print(f"  Reference: {order.get('purchase_order_number', 'N/A')}")
            print(f"  Line Items: {len(order.get('line_items', []))}")
            print(f"  Special Instructions: {order.get('special_instructions', 'None')[:100]}...")
            print(f"  Total: ${order.get('total_amount', 'N/A')} {order.get('currency', 'AUD')}")
        
        # Demo 3: Non-Order Email (Should not extract order)
        print_section("📧 Demo 3: Non-Order Email")
        
        non_order_email = """
        Subject: Meeting Follow-up and Next Steps
        From: <EMAIL>
        
        Hi Team,
        
        Thanks for the productive meeting yesterday. Here are the key takeaways:
        
        1. Q3 sales targets are on track
        2. New product launch scheduled for August
        3. Team building event planned for September
        
        Action items:
        - Sarah: Update the project timeline
        - Mike: Prepare budget proposal
        - Lisa: Coordinate with marketing team
        
        Next meeting: Friday 2 PM in Conference Room B
        
        Best regards,
        David Smith
        Operations Manager
        """
        
        result3 = await llm_service.process_email(
            email_body=non_order_email,
            subject="Meeting Follow-up and Next Steps",
            sender="<EMAIL>",
            email_id="demo-003"
        )
        
        print("📊 Results:")
        print(f"  Summary: {result3.get('summary', {}).get('summary', 'N/A')}")
        print(f"  Order Found: {'✅ Yes' if result3.get('order') else '❌ No (Expected)'}")
        
        # Demo 4: Markdown Summary Generation
        print_section("📝 Demo 4: Markdown Summary Generation")
        
        markdown_summary = await llm_service.generate_markdown_summary(
            email_body=simple_po,
            subject="Purchase Order PO-2025-100",
            sender="<EMAIL>"
        )
        
        print("Generated Markdown Summary:")
        print(markdown_summary[:500] + "..." if len(markdown_summary) > 500 else markdown_summary)
        
        # Demo 5: Direct Order Extraction
        print_section("🔍 Demo 5: Direct Order Extraction")
        
        raw_order_text = """
        Purchase Order Details:
        Customer: Tech Solutions Ltd
        PO: PO-2025-TECH-001
        
        Line Items:
        1. Laptops (Dell XPS 13): 10 units @ $1,200.00
        2. Monitors (27" 4K): 10 units @ $350.00
        3. Keyboards (Mechanical): 10 units @ $120.00
        4. Mice (Wireless): 10 units @ $45.00
        
        Delivery: 123 Tech Park, Sydney NSW 2000
        Total: $17,150.00
        """
        
        extracted_order = await llm_service.extract_order_from_content(raw_order_text)
        
        if extracted_order:
            print("✅ Order extraction successful")
            print(f"  Customer: {extracted_order.get('customer_details', {}).get('customer_name', 'N/A')}")
            print(f"  PO Number: {extracted_order.get('purchase_order_number', 'N/A')}")
            print(f"  Items: {len(extracted_order.get('line_items', []))}")
            print(f"  Total: ${extracted_order.get('total_amount', 'N/A')} {extracted_order.get('currency', 'AUD')}")
            
            # Demo 6: MYOB Payload Generation
            print_section("💼 Demo 6: MYOB Payload Generation")
            
            myob_payload = llm_service.generate_myob_payload(extracted_order)
            print("✅ MYOB payload generated successfully")
            print(f"  Payload Structure: {list(myob_payload.keys())}")
            print(f"  Debtor ID: {myob_payload.get('debtorid', 'N/A')}")
            print(f"  Order Number: {myob_payload.get('customerordernumber', 'N/A')}")
            print(f"  Line Count: {len(myob_payload.get('lines', []))}")
            
            # Show first line item structure
            if myob_payload.get('lines'):
                first_line = myob_payload['lines'][0]
                print(f"  Sample Line: {first_line.get('description', 'N/A')} - Qty: {first_line.get('quantity', 'N/A')}")
        else:
            print("❌ No order data extracted")
        
        print_section("🎯 Demo Summary")
        print("✅ All LLM Service capabilities demonstrated successfully!")
        print("✅ Email processing with order extraction")
        print("✅ Complex order handling with special instructions")
        print("✅ Non-order email detection")
        print("✅ Markdown summary generation")
        print("✅ Direct content order extraction")
        print("✅ MYOB payload generation")
        print("\n🚀 The LLM Service is ready for production use!")

async def demo_error_handling():
    """Demonstrate error handling capabilities."""
    print_section("🛡️ Error Handling Demo")
    
    async with LLMService() as llm_service:
        try:
            # Test with malformed content
            result = await llm_service.extract_order_from_content("")
            print(f"Empty content result: {result}")
            
            # Test with very short content
            result = await llm_service.extract_order_from_content("Hello")
            print(f"Short content result: {result}")
            
            print("✅ Error handling working correctly")
            
        except Exception as e:
            print(f"❌ Error occurred: {e}")

def main():
    """Main function to run all demos."""
    print("🎯 LLM SERVICE COMPREHENSIVE DEMO")
    print("This demo showcases all capabilities of the refactored LLM service")
    
    # Run the main demo
    asyncio.run(demo_email_processing())
    
    # Run error handling demo
    asyncio.run(demo_error_handling())

if __name__ == "__main__":
    main()