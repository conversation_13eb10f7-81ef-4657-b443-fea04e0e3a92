#!/usr/bin/env python3
"""
Test script for the LLM Service
This script demonstrates how to use the refactored LLM service.
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the LLM service
from llm_service import LLMService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_llm_service():
    """Test the LLM service functionality."""
    
    print("🚀 Starting LLM Service Test")
    print("=" * 50)
    
    try:
        # Initialize the service
        async with LLMService() as llm_service:
            print("✅ LLM Service initialized successfully")
            
            # Test health check
            health = await llm_service.health_check()
            print(f"🏥 Health Check: {health}")
            
            if not health.get('mistral', False):
                print("❌ Mistral service is not healthy. Check your API key.")
                return
            
            # Test email processing with a sample email
            sample_email = """
            Subject: Purchase Order #PO-2025-001
            From: <EMAIL>
            
            Dear Team,
            
            Please process the following purchase order:
            
            Customer: ABC Company
            Order Number: PO-2025-001
            
            Items:
            - Widget A: Quantity 10, Unit Price $25.00
            - Widget B: Quantity 5, Unit Price $50.00
            
            Delivery Address:
            123 Main Street
            Sydney NSW 2000
            Australia
            
            Total: $500.00
            
            Please confirm receipt.
            
            Best regards,
            John Smith
            """
            
            print("\n📧 Processing sample email...")
            print("-" * 30)
            
            result = await llm_service.process_email(
                email_body=sample_email,
                subject="Purchase Order #PO-2025-001",
                sender="<EMAIL>",
                email_id="test-001"
            )
            
            print("📊 Processing Results:")
            print(f"Summary: {result.get('summary', {})}")
            print(f"Order extracted: {'Yes' if result.get('order') else 'No'}")
            
            if result.get('order'):
                order = result['order']
                print(f"Customer: {order.get('customer_details', {}).get('customer_name', 'N/A')}")
                print(f"PO Number: {order.get('purchase_order_number', 'N/A')}")
                print(f"Line Items: {len(order.get('line_items', []))}")
                print(f"Total: {order.get('total_amount', 'N/A')} {order.get('currency', 'AUD')}")
            
            if result.get('myob_payload'):
                print("💼 MYOB payload generated successfully")
            
            # Test markdown summary generation
            print("\n📝 Generating markdown summary...")
            markdown_summary = await llm_service.generate_markdown_summary(
                email_body=sample_email,
                subject="Purchase Order #PO-2025-001",
                sender="<EMAIL>"
            )
            
            print("Markdown Summary:")
            print("-" * 20)
            print(markdown_summary[:200] + "..." if len(markdown_summary) > 200 else markdown_summary)
            
            print("\n✅ All tests completed successfully!")
            
    except Exception as e:
        logger.error(f"Error during testing: {e}")
        print(f"❌ Test failed: {e}")

async def test_individual_components():
    """Test individual components of the LLM service."""
    
    print("\n🔧 Testing Individual Components")
    print("=" * 50)
    
    try:
        async with LLMService() as llm_service:
            
            # Test order extraction only
            sample_content = """
            Purchase Order: PO-2025-002
            Customer: XYZ Corp
            Items:
            - Product A: 20 units @ $15.00 each
            - Product B: 10 units @ $30.00 each
            Total: $600.00
            """
            
            print("🔍 Testing order extraction...")
            order_data = await llm_service.extract_order_from_content(sample_content)
            
            if order_data:
                print("✅ Order extraction successful")
                print(f"Customer: {order_data.get('customer_details', {}).get('customer_name', 'N/A')}")
                print(f"Items: {len(order_data.get('line_items', []))}")
                
                # Test MYOB payload generation
                print("💼 Testing MYOB payload generation...")
                myob_payload = llm_service.generate_myob_payload(order_data)
                print("✅ MYOB payload generated successfully")
                print(f"Payload keys: {list(myob_payload.keys())}")
            else:
                print("❌ No order data extracted")
                
    except Exception as e:
        logger.error(f"Error testing components: {e}")
        print(f"❌ Component test failed: {e}")

def main():
    """Main function to run all tests."""
    print("🎯 LLM Service Test Suite")
    print("=" * 50)
    
    # Check if required environment variables are set
    if not os.getenv('MISTRAL_API_KEY'):
        print("❌ MISTRAL_API_KEY not found in environment variables")
        print("Please check your .env file")
        return
    
    print(f"🔑 Using Mistral API Key: {os.getenv('MISTRAL_API_KEY')[:10]}...")
    
    # Run the tests
    asyncio.run(test_llm_service())
    asyncio.run(test_individual_components())

if __name__ == "__main__":
    main()