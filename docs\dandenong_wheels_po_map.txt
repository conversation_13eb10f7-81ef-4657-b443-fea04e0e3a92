DANDENONG WHEELS AND CASTORS PURCHASE ORDERS - 2996

Okay, "Team Systems" is your company, and this is an Order from "Dandenong Wheels & Castors".
Here are the fields I would define as important for your company to process this order, along with why:
I. Core Order Identification & Tracking:
PURCHASE #: *00080396*
Why important: This is the primary reference number for this order. Quote this number for all communication regarding this order.
DATE: 5/06/2025
Why important: Establishes when the order was placed.
SHIP VIA: Pick Up
Why important: Indicates that the buyer (Dandenong Wheels & Castors) will arrange for collection of the goods from your location. Crucial for your dispatch planning.
TERMS: Net 30th after EOM
Why important: Specifies the payment terms agreed for this order.

II. Your Company's Information (as the Supplier):
To (Team Systems) Address:
121 Logis Blvd, Dandenong South VIC 3175
Why important: Your company's address as recorded by the buyer. Verify for accuracy.
SUPPLIER FAX NO: 9799 9422
Why important: Your company's fax number, often used by buyers to send orders.

III. Product/Service Details:
Item 1:
QTY.: 8
ITEM NO.: TSFRAME300
DESCRIPTION: 300MM X 3000MM CONVEYOR FRAME 75MM PITCH***(NETT PRICE ITEM)***
PRICE: $147.4725
UNIT: EA
EXTENDED: $1,179.78
Why important: Details the first product requested: quantity, your internal item number (ITEM NO.), description, unit price (excl. GST), unit of measure, and line total (excl. GST). Essential for picking, packing, and invoicing.

Item 2:
QTY.: 160
ITEM NO.: TSSROLL300
DESCRIPTION: 230MM X 48MM STEEL CONVEYOR ROLLER 120KG CAPACITY ***(NETT PRICE ITEM)***
PRICE: $9.80
UNIT: EA
EXTENDED: $1,568.00
Why important: Details the second product requested: quantity, your internal item number (ITEM NO.), description, unit price (excl. GST), unit of measure, and line total (excl. GST). Essential for picking, packing, and invoicing.

IV. Financial & Invoicing Information:
SALE AMT.: $2,747.78
Why important: The total amount for goods/services, excluding tax (sum of EXTENDED line items).
GST: $274.78
Why important: The total Goods and Services Tax amount.
TOTAL AMT.: $3,022.56
Why important: The grand total amount you will bill Dandenong Wheels & Castors for this order, including GST. Crucial for your invoicing and financial records.
PAID TODAY: $0.00
BALANCE DUE: $3,022.56
Why important: Confirms no payment has been made upfront and the full amount is due according to terms.
Payment Terms: Net 30th after EOM (Also listed above)

V. Delivery Information & Special Instructions:
Ship To:
Dandenong Wheels & Castors
310 Sth Gippsland Hwy
Dandenong South, VIC 3175
Why important: The address where the buyer will collect the goods. Essential for having the order ready at the correct location.
SHIP VIA: Pick Up (Also listed above)
COMMENT: PLEASE CALL WHEN READY
Why important: Action required before the buyer collects the goods.

VI. Other Important Instructions/Notes:
*PLEASE ACKNOWLEDGE RECEIPT OF THIS ORDER VIA RETURN EMAIL
Why important: Specific instruction to confirm receipt of the order.
*ADVISE DELIVERY DATE OR COLLECTION TIME/DATE
Why important: Action required - inform the buyer when the goods will be ready for pickup.
*ADVISE OF ANY BACK-ORDERS
Why important: Action required - inform the buyer if any items are not in stock and need to be back-ordered.
*ADVISE OF ANY PRICE CHANGES/ERRORS
Why important: Action required - inform the buyer if the prices on the order are incorrect.

In summary, for "Team Systems" as the supplier, the most critical fields for this PO are:
Order Identification: PURCHASE #, DATE, SHIP VIA (Pick Up), TERMS.
What to Supply: ITEM NO., DESCRIPTION, QTY., PRICE, EXTENDED for each line item.
Where to Deliver/Be Ready For Pickup: Ship To Address, SHIP VIA.
How to Get Paid: SALE AMT., GST, TOTAL AMT., TERMS.
Special Instructions: COMMENT (Call When Ready), Important Notes (Acknowledge, Advise Dates/Times, Back-orders, Price Changes).

This order requires proactive communication regarding readiness for pickup and stock status.

--- END OF FILE Dandenong_Wheels_Castors_Order_00080396.txt ---