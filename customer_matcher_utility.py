#!/usr/bin/env python3
"""
Customer Matcher Utility - Interactive tool for testing and managing customer matching
"""

import logging
import sys
from debtor_lookup_service import DebtorLookupService

def interactive_customer_matcher():
    """Interactive customer matching utility"""
    
    # Setup logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise for interactive use
    
    try:
        service = DebtorLookupService(csv_path="docs/active_debtors.csv", threshold=80)
        
        print("=" * 60)
        print("🔍 CUSTOMER MATCHER UTILITY")
        print("=" * 60)
        
        # Show stats
        stats = service.get_stats()
        print(f"📊 Loaded {stats['total_customers']} customers")
        print(f"📋 {stats['customers_with_instructions']} have parsing instructions")
        print(f"⚙️  Fuzzy match threshold: {stats['threshold']}%")
        print()
        
        while True:
            print("Choose an option:")
            print("1. Test email address matching")
            print("2. Search customers by name")
            print("3. Get customer by debtor ID")
            print("4. Show service statistics")
            print("5. Exit")
            
            choice = input("\nEnter choice (1-5): ").strip()
            
            if choice == '1':
                test_email_matching(service)
            elif choice == '2':
                search_customers(service)
            elif choice == '3':
                get_customer_by_id(service)
            elif choice == '4':
                show_statistics(service)
            elif choice == '5':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
            
            print()
    
    except Exception as e:
        print(f"❌ Error initializing service: {e}")
        return 1
    
    return 0


def test_email_matching(service):
    """Test email address matching"""
    print("\n📧 EMAIL MATCHING TEST")
    print("-" * 40)
    
    email = input("Enter email address: ").strip()
    if not email:
        print("❌ Email address required")
        return
    
    sender_name = input("Enter sender name (optional): ").strip() or None
    
    print(f"\n🔍 Searching for: {email}")
    if sender_name:
        print(f"👤 Sender name: {sender_name}")
    
    result = service.find_debtor_by_email(email, sender_name)
    
    if result:
        print("\n✅ MATCH FOUND!")
        print(f"🏢 Customer: {result['customer_name']}")
        print(f"🆔 Debtor ID: {result['debtor_id']}")
        print(f"📊 Confidence: {result['confidence_score']}%")
        print(f"🔧 Method: {result['match_method']}")
        print(f"🎯 Matched: {result['matched_text']}")
        
        if result['instructions_path']:
            print(f"📋 Instructions: {result['instructions_path']}")
            
            # Ask if user wants to see instructions
            show_instructions = input("\nShow parsing instructions? (y/n): ").strip().lower()
            if show_instructions == 'y':
                try:
                    with open(result['instructions_path'], 'r', encoding='utf-8') as f:
                        instructions = f.read()
                    print(f"\n📄 PARSING INSTRUCTIONS:")
                    print("-" * 30)
                    print(instructions)
                except Exception as e:
                    print(f"❌ Could not read instructions: {e}")
        else:
            print("📋 Instructions: None")
    else:
        print("\n❌ NO MATCH FOUND")
        
        # Suggest similar customers
        domain = email.split('@')[-1] if '@' in email else email
        suggestions = service.search_customers(domain.split('.')[0], limit=5)
        
        if suggestions:
            print(f"\n💡 Similar customers found:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion['customer_name']} (ID: {suggestion['debtor_id']}, Score: {suggestion['confidence_score']}%)")


def search_customers(service):
    """Search customers by name"""
    print("\n🔍 CUSTOMER SEARCH")
    print("-" * 40)
    
    query = input("Enter search query: ").strip()
    if not query:
        print("❌ Search query required")
        return
    
    limit = input("Max results (default 10): ").strip()
    try:
        limit = int(limit) if limit else 10
    except ValueError:
        limit = 10
    
    print(f"\n🔍 Searching for: '{query}'")
    results = service.search_customers(query, limit=limit)
    
    if results:
        print(f"\n✅ Found {len(results)} matches:")
        for i, result in enumerate(results, 1):
            print(f"{i:2d}. {result['customer_name']}")
            print(f"     ID: {result['debtor_id']} | Score: {result['confidence_score']}% | Variant: {result['matched_variant']}")
            if result['instructions_path']:
                print(f"     Instructions: {result['instructions_path']}")
    else:
        print("❌ No matches found")


def get_customer_by_id(service):
    """Get customer by debtor ID"""
    print("\n🆔 CUSTOMER LOOKUP BY ID")
    print("-" * 40)
    
    debtor_id = input("Enter debtor ID: ").strip()
    try:
        debtor_id = int(debtor_id)
    except ValueError:
        print("❌ Invalid debtor ID. Must be a number.")
        return
    
    customer = service.get_customer_by_debtor_id(debtor_id)
    
    if customer:
        print(f"\n✅ CUSTOMER FOUND:")
        print(f"🏢 Name: {customer.account_name}")
        print(f"🆔 Debtor ID: {customer.debtor_id}")
        
        if customer.instructions_po_path:
            print(f"📋 Instructions: {customer.instructions_po_path}")
        else:
            print("📋 Instructions: None")
        
        print(f"🔍 Search variants: {', '.join(customer.search_variants)}")
        print(f"🏷️  Keywords: {', '.join(customer.domain_keywords)}")
    else:
        print(f"❌ No customer found with debtor ID: {debtor_id}")


def show_statistics(service):
    """Show service statistics"""
    print("\n📊 SERVICE STATISTICS")
    print("-" * 40)
    
    stats = service.get_stats()
    for key, value in stats.items():
        print(f"{key.replace('_', ' ').title()}: {value}")


if __name__ == "__main__":
    exit(interactive_customer_matcher())