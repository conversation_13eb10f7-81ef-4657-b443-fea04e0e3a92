---
url: "https://developer.myob.com/api/exo/endpoints/stock/stocklocation/"
title: "Stock Location"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Stock Location

Return stock locations

**Date Released:** Nov 20th 2013 **Date Updated:** Nov 20th 2013

| URL | Supports |
| --- | --- |
| {URI}/stocklocation | [GET](https://developer.myob.com/api/exo/endpoints/stock/stocklocation/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/stock/stocklocation/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/stock/stocklocation/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

The elements list below details information for Stock Location. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/stock/stocklocation/#reveal)

#### Attribute Details

- code string,null
- Type: string,null
- name string,null
- Type: string,null
- isactive boolean
- Type: boolean
- excludefromvaluation boolean
- Type: boolean
- excludefromfreestock boolean
- Type: boolean
- excludefromsales boolean
- Type: boolean
- rel string,null
- Type: string,null
- title string,null
- Type: string,null
- id integer
- Required on PUT
- href string,null
- Type: string,null

#### Example json GET response

- {
  - code : AK
  - name : Auckland
  - isactive : true
  - excludefromvaluation : false
  - excludefromfreestock : false
  - excludefromsales : false
  - id : 1
  - href : {URI}/stocklocation/1
- }

{URI} is defined as: http://exo.api.myob.com/

|     |     |
| --- | --- |
|  |  |