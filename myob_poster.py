"""
MYOB Order Poster - Recreated version with enhanced validation.
Pulls orders from the myob folder, validates them, and posts to MYOB.
"""
import json
import os
import logging
import time
import smtplib
from typing import Optional, Dict, Any, List, Tuple
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart

from myob_service import MyobService
from config import config

logger = logging.getLogger(__name__)

class MYOBPoster:
    """Enhanced MYOB poster with validation and order management."""
    
    def __init__(self):
        self.myob_service = MyobService()
        self.myob_dir = "myob"
        os.makedirs(self.myob_dir, exist_ok=True)
        logger.info(f"Initialized MYOB poster with directory: {self.myob_dir}")

    def list_pending_orders(self) -> List[str]:
        """List all pending orders in the myob directory."""
        if not os.path.exists(self.myob_dir):
            logger.warning(f"MYOB directory {self.myob_dir} does not exist")
            return []
        
        orders = []
        try:
            for filename in os.listdir(self.myob_dir):
                if filename.endswith(".json"):
                    order_id = filename.replace(".json", "")
                    orders.append(order_id)
            logger.info(f"Found {len(orders)} pending orders")
            return sorted(orders)
        except Exception as e:
            logger.error(f"Error listing pending orders: {e}")
            return []

    def load_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Load order data from JSON file."""
        order_file = os.path.join(self.myob_dir, f"{order_id}.json")
        if not os.path.exists(order_file):
            logger.error(f"Order file not found: {order_file}")
            return None
        
        try:
            with open(order_file, 'r', encoding='utf-8') as f:
                order_data = json.load(f)
            logger.info(f"Successfully loaded order: {order_id}")
            return order_data
        except Exception as e:
            logger.error(f"Error loading order {order_id}: {e}")
            return None

    def validate_order_data(self, order_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate order data structure before sending to MYOB.
        Returns (is_valid, list_of_errors)
        """
        errors = []
        
        # Check required fields
        required_fields = ["debtorid", "status", "lines"]
        for field in required_fields:
            if field not in order_data:
                errors.append(f"Missing required field: {field}")
        
        # Validate debtor ID
        if "debtorid" in order_data:
            if not isinstance(order_data["debtorid"], int) or order_data["debtorid"] <= 0:
                errors.append("Invalid debtor ID: must be a positive integer")
        
        # Validate status
        if "status" in order_data:
            valid_statuses = [0, 3]  # Common MYOB status values
            if order_data["status"] not in valid_statuses:
                errors.append(f"Invalid status: {order_data['status']} (valid: {valid_statuses})")
        
        # Validate lines
        if "lines" in order_data:
            if not isinstance(order_data["lines"], list) or len(order_data["lines"]) == 0:
                errors.append("Order lines must be a non-empty list")
            else:
                for i, line in enumerate(order_data["lines"]):
                    if not isinstance(line, dict):
                        errors.append(f"Line {i+1}: must be an object")
                        continue
                    
                    if "stockcode" not in line:
                        errors.append(f"Line {i+1}: missing stockcode")
                    elif not line["stockcode"]:
                        errors.append(f"Line {i+1}: stockcode cannot be empty")
                    
                    if "orderquantity" not in line:
                        errors.append(f"Line {i+1}: missing orderquantity")
                    elif not isinstance(line["orderquantity"], (int, float)) or line["orderquantity"] <= 0:
                        errors.append(f"Line {i+1}: orderquantity must be a positive number")
        
        # Validate delivery address if present
        if "deliveryaddress" in order_data:
            addr = order_data["deliveryaddress"]
            if not isinstance(addr, dict):
                errors.append("Delivery address must be an object")
            elif not any(addr.get(f"line{i}") for i in range(1, 7)):
                errors.append("Delivery address must have at least one non-empty line")
        
        is_valid = len(errors) == 0
        return is_valid, errors

    def display_order_summary(self, order_id: str, order_data: Dict[str, Any]) -> None:
        """Display a formatted summary of the order."""
        print(f"\n{'='*60}")
        print(f"ORDER REVIEW: {order_id}")
        print(f"{'='*60}")
        
        # Basic order info
        print(f"📋 Debtor ID: {order_data.get('debtorid', 'N/A')}")
        print(f"📋 Status: {order_data.get('status', 'N/A')}")
        print(f"📋 Customer PO: {order_data.get('customerordernumber', 'N/A')}")
        
        # Delivery address
        if order_data.get('deliveryaddress'):
            addr = order_data['deliveryaddress']
            print(f"\n🏠 DELIVERY ADDRESS:")
            for i in range(1, 7):
                line = addr.get(f'line{i}')
                if line:
                    print(f"   {line}")
        
        # Order lines
        lines = order_data.get('lines', [])
        print(f"\n📦 ORDER LINES ({len(lines)} items):")
        for i, line in enumerate(lines, 1):
            stockcode = line.get('stockcode', 'N/A')
            quantity = line.get('orderquantity', 'N/A')
            print(f"   {i}. {stockcode} - Qty: {quantity}")
        
        # Extra fields
        if order_data.get('extrafields'):
            print(f"\n🔧 EXTRA FIELDS:")
            for field in order_data['extrafields']:
                key = field.get('key', 'N/A')
                value = field.get('value', 'N/A')
                print(f"   {key}: {value}")

    def validate_with_myob(self, order_data: Dict[str, Any]) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        Validate order with MYOB API.
        Returns (success, validated_order, message)
        """
        try:
            logger.info("Validating order with MYOB API")
            validation_result = self.myob_service.validate_order(order_data)
            
            if not validation_result or 'order' not in validation_result:
                return False, None, "MYOB validation failed: No order returned"
            
            validated_order = validation_result['order']
            return True, validated_order, "Order validated successfully"
            
        except Exception as e:
            error_msg = f"MYOB validation error: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg

    def post_order_to_myob(self, order_id: str, order_data: Optional[Dict[str, Any]] = None) -> Tuple[bool, str]:
        """
        Post order to MYOB with full validation.
        Returns (success, message)
        """
        if order_data is None:
            order_data = self.load_order(order_id)
            if not order_data:
                return False, f"Could not load order {order_id}"
        
        # Step 1: Basic validation
        print(f"\n🔍 Validating order structure...")
        is_valid, errors = self.validate_order_data(order_data)
        if not is_valid:
            error_msg = f"Order validation failed:\n" + "\n".join(f"  - {error}" for error in errors)
            logger.error(error_msg)
            print(f"❌ {error_msg}")
            return False, error_msg
        
        print("✅ Order structure validation passed")
        
        # Step 2: MYOB API validation
        print(f"🔍 Validating with MYOB API...")
        success, validated_order, message = self.validate_with_myob(order_data)
        if not success:
            logger.error(f"MYOB validation failed: {message}")
            print(f"❌ {message}")
            return False, message
        
        print("✅ MYOB validation passed")
        
        # Step 3: Create order
        print(f"📤 Creating order in MYOB...")
        try:
            if validated_order is None:
                error_msg = "Validated order is None"
                logger.error(error_msg)
                print(f"❌ {error_msg}")
                return False, error_msg
            
            result = self.myob_service.create_order(validated_order)
            if result:
                success_msg = f"Order {order_id} posted successfully! MYOB ID: {result.get('id', 'Unknown')}"
                logger.info(success_msg)
                print(f"✅ {success_msg}")
                return True, success_msg
            else:
                error_msg = f"Failed to create order {order_id} in MYOB"
                logger.error(error_msg)
                print(f"❌ {error_msg}")
                return False, error_msg
                
        except Exception as e:
            error_msg = f"Error creating order {order_id}: {str(e)}"
            logger.error(error_msg)
            print(f"❌ {error_msg}")
            return False, error_msg

    def review_order(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Load and display order for review."""
        order_data = self.load_order(order_id)
        if not order_data:
            return None
        
        self.display_order_summary(order_id, order_data)
        return order_data

def send_completion_email(subject: str, body: str, recipient: Optional[str] = None) -> None:
    """Send completion notification email."""
    recipient = recipient or os.getenv('LOG_RECIPIENT_EMAIL')
    if not recipient:
        logger.warning('No LOG_RECIPIENT_EMAIL set, skipping notification email.')
        return
    
    msg = MIMEMultipart()
    msg['From'] = os.getenv('USER_EMAIL', '<EMAIL>')
    msg['To'] = recipient
    msg['Subject'] = subject
    msg.attach(MIMEText(body, 'plain'))
    
    try:
        with smtplib.SMTP('localhost') as server:
            server.send_message(msg)
        logger.info(f'Notification email sent to {recipient}')
    except Exception as e:
        logger.error(f'Failed to send notification email: {e}')

def batch_post_orders(directory: Optional[str] = None) -> List[Tuple[str, bool, str]]:
    """
    Post all orders from the specified directory.
    Returns list of (filename, success, message) tuples.
    """
    if directory is None:
        directory = "myob"
    
    if not os.path.exists(directory):
        logger.error(f"Directory {directory} does not exist")
        return []
    
    poster = MYOBPoster()
    files = [f for f in os.listdir(directory) if f.endswith('.json')]
    files.sort()
    
    if not files:
        logger.info("No JSON files found in directory")
        return []
    
    results = []
    print(f"\n🚀 Starting batch processing of {len(files)} orders...")
    
    for idx, filename in enumerate(files, 1):
        filepath = os.path.join(directory, filename)
        order_id = filename.replace('.json', '')
        
        print(f"\n[{idx}/{len(files)}] Processing: {order_id}")
        print("-" * 50)
        
        try:
            success, message = poster.post_order_to_myob(order_id)
            results.append((filename, success, message))
            
            if success:
                print(f"✅ {order_id} completed successfully")
            else:
                print(f"❌ {order_id} failed: {message}")
                
        except Exception as e:
            error_msg = f"Exception processing {filename}: {str(e)}"
            logger.error(error_msg)
            print(f"❌ {error_msg}")
            results.append((filename, False, error_msg))
        
        # Small delay to avoid overwhelming the API
        if idx < len(files):
            time.sleep(1)
    
    # Send completion email
    successful = sum(1 for _, success, _ in results if success)
    failed = len(results) - successful
    
    summary_lines = []
    for filename, success, message in results:
        status = "✅" if success else "❌"
        summary_lines.append(f"{status} {filename}: {message}")
    
    summary = "\n".join(summary_lines)
    
    email_body = f"""MYOB Batch Posting Complete

Summary:
- Total orders: {len(results)}
- Successful: {successful}
- Failed: {failed}

Details:
{summary}
"""
    
    send_completion_email("MYOB Batch Posting Complete", email_body)
    
    print(f"\n📊 BATCH SUMMARY:")
    print(f"   Total: {len(results)}")
    print(f"   Successful: {successful}")
    print(f"   Failed: {failed}")
    
    return results

def interactive_mode():
    """Interactive CLI for reviewing and posting orders."""
    poster = MYOBPoster()
    
    print(f"\n{'='*60}")
    print("🏢 MYOB ORDER POSTER")
    print("Review and post orders to MYOB with validation")
    print(f"{'='*60}")
    
    while True:
        pending_orders = poster.list_pending_orders()
        
        if not pending_orders:
            print("\n📭 No orders pending review.")
            print("💡 Run the order processor first to extract orders from emails.")
            break
        
        print(f"\n📋 Pending orders: {len(pending_orders)}")
        for i, order_id in enumerate(pending_orders, 1):
            print(f"   {i}. {order_id}")
        
        print(f"\n🎯 Options:")
        print(f"   • Enter order number (1-{len(pending_orders)}) to review")
        print(f"   • Type 'batch' to process all orders")
        print(f"   • Type 'q' to quit")
        
        try:
            choice = input("\n👉 Your choice: ").strip()
            
            if choice.lower() == 'q':
                break
            elif choice.lower() == 'batch':
                print(f"\n🚀 Starting batch processing...")
                batch_post_orders()
                break
            
            try:
                order_index = int(choice) - 1
            except ValueError:
                print("❌ Invalid choice. Please enter a number, 'batch', or 'q'.")
                continue
                
            if 0 <= order_index < len(pending_orders):
                order_id = pending_orders[order_index]
                
                # Review order
                order_data = poster.review_order(order_id)
                if not order_data:
                    continue
                
                # Ask for action
                while True:
                    action = input("\n🎯 Actions: [p]ost to MYOB, [s]kip, [q]uit: ").strip().lower()
                    if action in ['p', 's', 'q']:
                        break
                    print("❌ Invalid choice. Please enter 'p', 's', or 'q'.")
                
                if action == 'p':
                    success, message = poster.post_order_to_myob(order_id, order_data)
                    if success:
                        print(f"🎉 Order {order_id} processed successfully!")
                    else:
                        print(f"💥 Failed to process order {order_id}")
                        
                elif action == 'q':
                    break
                # 's' skips to next order
                
            else:
                print("❌ Invalid choice.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Exiting...")
            break
    
    print("\n👋 Goodbye!")

def main():
    """Main entry point."""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'batch':
            directory = sys.argv[2] if len(sys.argv) > 2 else None
            batch_post_orders(directory)
        elif sys.argv[1] == 'single':
            if len(sys.argv) < 3:
                print("Usage: python myob_poster.py single <order_id>")
                return
            
            order_id = sys.argv[2]
            poster = MYOBPoster()
            success, message = poster.post_order_to_myob(order_id)
            if success:
                print(f"✅ Success: {message}")
            else:
                print(f"❌ Failed: {message}")
        else:
            print("Usage:")
            print("  python myob_poster.py                    # Interactive mode")
            print("  python myob_poster.py batch [directory]  # Batch process all orders")
            print("  python myob_poster.py single <order_id>  # Process single order")
    else:
        interactive_mode()

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    main()