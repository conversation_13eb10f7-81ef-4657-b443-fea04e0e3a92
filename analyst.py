"""
Dynamic AI Agent for MYOB EXO ERP Purchase Order Management
Expert in analyzing stock levels, forecasting demand, and managing purchase orders from overseas suppliers.
"""

import json
import logging
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from config import config
from myob_service import MyobService
from enhanced_supabase_memory_client import get_enhanced_memory_client
# Removed exo_api import - now using static endpoints

logger = logging.getLogger(__name__)

class EnumEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles Enum objects by converting them to their string values"""
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        return super().default(obj)

class PriorityLevel(Enum):
    """Priority levels for purchase order recommendations"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class StockAnalysis:
    """Stock analysis data structure"""
    stockcode: str
    description: str
    current_stock: float
    reorder_level: float
    reorder_quantity: float
    cost_price: float
    supplier_id: str
    supplier_name: str
    lead_time_days: int
    priority: PriorityLevel
    forecast_demand: float
    recommended_order_qty: float
    days_until_stockout: int
    seasonal_factor: float = 1.0

@dataclass
class PurchaseOrderRecommendation:
    """Purchase order recommendation structure"""
    supplier_id: str
    supplier_name: str
    total_value: float
    line_items: List[Dict[str, Any]]
    priority: PriorityLevel
    justification: str
    expected_delivery_date: str
    risk_assessment: str

class MyobPurchaseOrderAgent:
    """Dynamic AI Agent for MYOB EXO ERP Purchase Order Management"""
    
    def __init__(self):
        self.myob_service = MyobService()
        self.memory_client = get_enhanced_memory_client()
        self.mistral_api_key = config.MISTRAL_API_KEY
        self.mistral_model = config.MISTRAL_MODEL
        self.base_url = config.MYOB_BASE_URL
        self.headers = config.MYOB_HEADERS
        
        # Rate limiting for API calls
        self.last_api_call = 0
        self.min_api_interval = 5.0  # Minimum 5 seconds between API calls
        self.api_retry_delay = 10.0  # 10 seconds delay on rate limit errors
        self.max_retries = 3
        
        # Initialize endpoint discovery
        self.available_endpoints = {}
        self.categorized_endpoints = {}
        self.purchase_order_capabilities = {}
        
        if not self.mistral_api_key:
            logger.warning("Mistral API key not found. Please set MISTRAL_API_KEY in your environment.")
        
        logger.info("Initialized MYOB Purchase Order Agent")
        
        # Automatically discover endpoints on initialization
        self._initialize_discovery()
    
    def _initialize_discovery(self):
        """Initialize endpoint discovery and capabilities analysis"""
        try:
            logger.info("Starting endpoint discovery...")
            
            # Discover all available endpoints
            self.available_endpoints = self.discover_endpoints()
            
            # Categorize endpoints for purchase order operations
            self.categorized_endpoints = self.analyze_available_endpoints()
            
            # Analyze purchase order capabilities
            self.purchase_order_capabilities = self.discover_purchase_order_capabilities()
            
            logger.info("Endpoint discovery completed successfully")
            
            # Log summary of discovered capabilities
            po_endpoints = len(self.categorized_endpoints.get("purchase_orders", []))
            stock_endpoints = len(self.categorized_endpoints.get("stock_management", []))
            supplier_endpoints = len(self.categorized_endpoints.get("suppliers", []))
            
            logger.info(f"Discovered capabilities: {po_endpoints} PO endpoints, {stock_endpoints} stock endpoints, {supplier_endpoints} supplier endpoints")
            
        except Exception as e:
            logger.warning(f"Endpoint discovery failed: {e}. Agent will continue with basic functionality.")
            # Initialize empty structures for fallback
            self.available_endpoints = {}
            self.categorized_endpoints = {}
            self.purchase_order_capabilities = {}
    
    def call_mistral_api(self, messages: List[Dict[str, str]], temperature: float = 0.3) -> str:
        """Call Mistral API for AI analysis with rate limiting and retry logic"""
        if not self.mistral_api_key:
            raise ValueError("Mistral API key not configured")
        
        # Rate limiting - ensure minimum interval between API calls
        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call
        if time_since_last_call < self.min_api_interval:
            sleep_time = self.min_api_interval - time_since_last_call
            logger.info(f"Rate limiting: sleeping for {sleep_time:.1f} seconds")
            time.sleep(sleep_time)
        
        url = "https://api.mistral.ai/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.mistral_api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.mistral_model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": 1500  # Reduced to minimize response time
        }
        
        for attempt in range(self.max_retries):
            try:
                self.last_api_call = time.time()
                response = requests.post(url, headers=headers, json=payload, timeout=90)  # Increased timeout
                
                if response.status_code == 429:  # Rate limit exceeded
                    if attempt < self.max_retries - 1:
                        # Exponential backoff: 10s, 20s, 40s
                        backoff_delay = self.api_retry_delay * (2 ** attempt)
                        logger.warning(f"Rate limit hit, waiting {backoff_delay} seconds before retry {attempt + 1}")
                        time.sleep(backoff_delay)
                        continue
                    else:
                        logger.error("Max retries reached for rate limiting")
                        raise requests.exceptions.HTTPError(f"429 Rate limit exceeded after {self.max_retries} attempts")
                
                response.raise_for_status()
                return response.json()["choices"][0]["message"]["content"]
                
            except requests.exceptions.Timeout:
                if attempt < self.max_retries - 1:
                    backoff_delay = self.api_retry_delay * (2 ** attempt)
                    logger.warning(f"Request timeout, retrying in {backoff_delay} seconds (attempt {attempt + 1})")
                    time.sleep(backoff_delay)
                    continue
                else:
                    logger.error("Max retries reached for timeouts")
                    raise
            except Exception as e:
                if attempt < self.max_retries - 1 and ("429" in str(e) or "timeout" in str(e).lower()):
                    backoff_delay = self.api_retry_delay * (2 ** attempt)
                    logger.warning(f"API error, retrying in {backoff_delay} seconds (attempt {attempt + 1}): {e}")
                    time.sleep(backoff_delay)
                    continue
                else:
                    logger.error(f"Mistral API call failed: {e}")
                    raise
        raise Exception("Mistral API call failed after multiple retries.")
    
    def call_mistral_api_with_fallback(self, messages: List[Dict[str, str]], temperature: float = 0.3, fallback_response: Optional[str] = None) -> str:
        """Call Mistral API with fallback for when rate limits are exceeded"""
        try:
            return self.call_mistral_api(messages, temperature)
        except Exception as e:
            logger.warning(f"Mistral API call failed: {e}")
            
            if fallback_response:
                logger.info("Using provided fallback response")
                return fallback_response
            
            # Generate a basic fallback response based on the prompt
            if any("priority" in msg.get("content", "").lower() for msg in messages):
                return "medium"
            elif any("forecast" in msg.get("content", "").lower() for msg in messages):
                return "10"
            elif any("analysis" in msg.get("content", "").lower() for msg in messages):
                return "Basic analysis: Item requires attention based on stock levels."
            else:
                return "Analysis unavailable due to API limitations."
    
    def get_api_status(self) -> Dict[str, Any]:
        """Get current API status and rate limiting information"""
        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call
        
        return {
            "last_call_time": self.last_api_call,
            "time_since_last_call": time_since_last_call,
            "min_interval": self.min_api_interval,
            "ready_for_call": time_since_last_call >= self.min_api_interval,
            "estimated_wait_time": max(0, self.min_api_interval - time_since_last_call)
        }
    
    def discover_endpoints(self) -> Dict[str, Any]:
        """Load all available MYOB EXO endpoints from static comprehensive file"""
        try:
            # Load from the comprehensive endpoints file
            with open('endpoints_comprehensive.json', 'r') as f:
                endpoints_data = json.load(f)
                
            # Convert to the expected format for backward compatibility
            endpoints = {
                "endpoints": endpoints_data.get("endpoints", []),
                "metadata": endpoints_data.get("metadata", {})
            }
            
            # Replace {URI} with actual base URL in endpoints
            for endpoint in endpoints["endpoints"]:
                if "href" in endpoint:
                    endpoint["href"] = endpoint["href"].replace("{URI}", self.base_url)
                if "search_href" in endpoint:
                    endpoint["search_href"] = endpoint["search_href"].replace("{URI}", self.base_url)
                if "report_href" in endpoint:
                    endpoint["report_href"] = endpoint["report_href"].replace("{URI}", self.base_url)
            
            # Store endpoint discovery in memory for future reference
            self.memory_client.add_memory(
                documents=[json.dumps(endpoints, indent=2, cls=EnumEncoder)],
                metadatas=[{
                    "type": "static_endpoint_discovery",
                    "date": datetime.now().isoformat(),
                    "total_endpoints": len(endpoints.get('endpoints', [])),
                    "source": "endpoints_comprehensive.json"
                }]
            )
            
            logger.info(f"Loaded {len(endpoints.get('endpoints', []))} MYOB EXO endpoints from static file")
            return endpoints
            
        except Exception as e:
            logger.warning(f"Failed to load comprehensive endpoints: {e}")
            # Fallback to original endpoints.json file
            try:
                with open('endpoints.json', 'r') as f:
                    endpoints = json.load(f)
                    logger.info(f"Loaded {len(endpoints.get('endpoints', []))} endpoints from fallback file")
                    return endpoints
            except Exception as e2:
                logger.error(f"Failed to load endpoints from any file: {e2}")
                return {}
    
    def analyze_available_endpoints(self) -> Dict[str, List[str]]:
        """Analyze discovered endpoints and categorize them for purchase order operations"""
        endpoints = self.discover_endpoints()
        
        if not endpoints or 'endpoints' not in endpoints:
            logger.warning("No endpoints discovered")
            return {}
        
        # Use AI to analyze endpoints and categorize them
        messages = [
            {"role": "system", "content": """You are an expert in MYOB EXO ERP API analysis. 
            Analyze the provided endpoints and categorize them for purchase order management operations.
            Focus on identifying endpoints related to:
            - Stock/Inventory management
            - Purchase orders
            - Suppliers/Creditors
            - Reports and analytics
            - Financial data
            """},
            {"role": "user", "content": f"""
            Analyze these MYOB EXO endpoints and categorize them for purchase order operations:
            
            {json.dumps(endpoints, indent=2)}
            
            Return a categorized list showing which endpoints are most relevant for:
            1. Stock analysis
            2. Purchase order creation
            3. Supplier management
            4. Financial reporting
            5. Inventory forecasting
            
            Format as JSON with categories as keys and relevant endpoint URLs as values.
            """}
        ]
        
        try:
            ai_analysis = self.call_mistral_api(messages)
            
            # Try to parse AI response as JSON
            try:
                categorized_endpoints = json.loads(ai_analysis)
            except json.JSONDecodeError:
                # If AI doesn't return valid JSON, create a basic categorization
                categorized_endpoints = self._basic_endpoint_categorization(endpoints)
            
            # Store categorized endpoints
            self.memory_client.add_memory(
                documents=[json.dumps(categorized_endpoints, indent=2)],
                metadatas=[{
                    "type": "endpoint_categorization",
                    "date": datetime.now().isoformat(),
                    "analysis_method": "ai_powered"
                }]
            )
            
            logger.info("Endpoints analyzed and categorized successfully")
            return categorized_endpoints
            
        except Exception as e:
            logger.error(f"Error analyzing endpoints: {e}")
            # Fallback to basic categorization
            return self._basic_endpoint_categorization(endpoints)
    
    def _basic_endpoint_categorization(self, endpoints: Dict[str, Any]) -> Dict[str, List[str]]:
        """Enhanced endpoint categorization using pre-categorized comprehensive endpoints"""
        
        # First try to use pre-categorized data from comprehensive endpoints
        try:
            with open('endpoints_categorized.json', 'r') as f:
                categorized_data = json.load(f)
                
            # Return the categorized endpoints directly
            if "categorized_endpoints" in categorized_data:
                logger.info("Using pre-categorized endpoints from comprehensive file")
                return categorized_data["categorized_endpoints"]
                
        except Exception as e:
            logger.warning(f"Could not load categorized endpoints, falling back to manual categorization: {e}")
        
        # Fallback to manual categorization if file loading fails
        categories = {
            "stock_management": [],
            "purchase_orders": [],
            "creditors": [],
            "suppliers": [],
            "financial": [],
            "reports": [],
            "search": [],
            "inventory_operations": [],
            "sales_analysis": [],
            "container_operations": [],
            "other": []
        }
        
        for endpoint in endpoints.get('endpoints', []):
            href = endpoint.get('href', '').lower()
            title = endpoint.get('title', '').lower()
            category = endpoint.get('category', '').lower()
            
            # Use pre-assigned category if available
            if category:
                if category == "stock_management":
                    categories["stock_management"].append(endpoint)
                elif category == "purchase_orders":
                    categories["purchase_orders"].append(endpoint)
                elif category == "creditors":
                    categories["creditors"].append(endpoint)
                elif category == "stock_operations":
                    categories["inventory_operations"].append(endpoint)
                elif category == "sales_orders":
                    categories["sales_analysis"].append(endpoint)
                elif category == "reports":
                    categories["reports"].append(endpoint)
                elif category == "reference_data":
                    categories["financial"].append(endpoint)
                elif category == "search_templates":
                    categories["search"].append(endpoint)
                else:
                    categories["other"].append(endpoint)
            else:
                # Enhanced categorization for comprehensive inventory management
                if 'purchaseorder' in href or 'purchase' in href or 'purchaseorder' in title:
                    categories["purchase_orders"].append(endpoint)
                elif 'creditor' in href or 'creditor' in title:
                    categories["creditors"].append(endpoint)
                elif 'salesorder' in href or 'sales' in href or 'salesorder' in title:
                    categories["sales_analysis"].append(endpoint)  # Sales orders for demand analysis
                elif 'stock/receipt' in href or 'stock/sale' in href or 'stock/transfer' in href or 'stock/adjust' in href:
                    categories["inventory_operations"].append(endpoint)  # Stock operations for container management
                elif 'stockitem' in href or 'stock/transaction' in href or 'stock/search' in href or 'stock' in href:
                    categories["stock_management"].append(endpoint)
                elif 'debtor' in href or 'debtor' in title:
                    categories["suppliers"].append(endpoint)  # Customers/suppliers for demand analysis
                elif 'report' in href or 'report' in title:
                    categories["reports"].append(endpoint)
                elif 'search' in href or 'search' in title or 'template' in href:
                    categories["search"].append(endpoint)
                elif any(term in href for term in ['financial', 'account', 'ledger', 'transaction', 'currency', 'payment']):
                    categories["financial"].append(endpoint)
                elif any(term in href for term in ['bom', 'classification', 'location', 'unitofmeasure']):
                    categories["container_operations"].append(endpoint)  # Supporting data for container imports
                else:
                    categories["other"].append(endpoint)
        
        return categories
    
    def get_enhanced_stock_data(self, stockcode: str) -> Dict[str, Any]:
        """Get comprehensive stock data using discovered endpoints"""
        stock_data = {}
        
        # Get categorized endpoints
        categorized_endpoints = self.analyze_available_endpoints()
        
        # Try to get stock data from multiple endpoints
        for endpoint_info in categorized_endpoints.get("stock_management", []):
            try:
                if isinstance(endpoint_info, dict):
                    endpoint_url = endpoint_info.get('href', '')
                    endpoint_title = endpoint_info.get('title', '')
                    
                    # Try different URL patterns for stock data
                    if stockcode and '{stockcode}' in endpoint_url:
                        url = endpoint_url.replace('{stockcode}', stockcode)
                    elif stockcode and endpoint_url.endswith('/stockitem'):
                        url = f"{endpoint_url}/{stockcode}"
                    else:
                        continue
                    
                    response = requests.get(url, headers=self.headers, timeout=30)
                    if response.status_code == 200:
                        data = response.json()
                        stock_data[endpoint_title] = data
                        logger.info(f"Retrieved data from {endpoint_title}")
                        
            except Exception as e:
                logger.debug(f"Could not retrieve data from endpoint {endpoint_info}: {e}")
                continue
        
        return stock_data
    
    def discover_purchase_order_capabilities(self) -> Dict[str, Any]:
        """Discover purchase order related capabilities"""
        categorized_endpoints = self.analyze_available_endpoints()
        
        capabilities = {
            "can_create_purchase_orders": False,
            "can_validate_purchase_orders": False,
            "can_search_purchase_orders": True,
            "available_purchase_endpoints": [],
            "supplier_endpoints": [],
            "stock_endpoints": []
        }
        
        # Check purchase order endpoints
        for endpoint_info in categorized_endpoints.get("purchase_orders", []):
            if isinstance(endpoint_info, dict):
                href = endpoint_info.get('href', '').lower()
                title = endpoint_info.get('title', '').lower()
                
                if 'validate' in href or 'validate' in title:
                    capabilities["can_validate_purchase_orders"] = True
                elif 'search' in href or 'search' in title:
                    capabilities["can_search_purchase_orders"] = True
                elif href.endswith('/purchaseorder') or 'create' in title:
                    capabilities["can_create_purchase_orders"] = True
                
                capabilities["available_purchase_endpoints"].append(endpoint_info)
        
        # Check supplier endpoints
        capabilities["supplier_endpoints"] = categorized_endpoints.get("suppliers", [])
        
        # Check stock endpoints
        capabilities["stock_endpoints"] = categorized_endpoints.get("stock_management", [])
        
        # Use AI to provide recommendations
        messages = [
            {"role": "system", "content": """You are an expert in MYOB EXO ERP purchase order management. 
            Analyze the discovered capabilities and provide recommendations for purchase order operations."""},
            {"role": "user", "content": f"""
            Based on these discovered MYOB EXO capabilities:
            
            {json.dumps(capabilities, indent=2)}
            
            Provide recommendations for:
            1. Best endpoints to use for purchase order creation
            2. How to integrate supplier data
            3. Stock analysis workflow
            4. Data validation approaches
            5. Potential limitations or considerations
            
            Format as a structured analysis with actionable recommendations.
            """}
        ]
        
        try:
            ai_recommendations = self.call_mistral_api(messages)
            capabilities["ai_recommendations"] = ai_recommendations
            
            # Store capabilities analysis
            self.memory_client.add_memory(
                documents=[json.dumps(capabilities, indent=2)],
                metadatas=[{
                    "type": "purchase_order_capabilities",
                    "date": datetime.now().isoformat(),
                    "has_po_creation": capabilities["can_create_purchase_orders"],
                    "has_validation": capabilities["can_validate_purchase_orders"]
                }]
            )
            
        except Exception as e:
            logger.error(f"Error generating AI recommendations: {e}")
            capabilities["ai_recommendations"] = "AI analysis unavailable"
        
        return capabilities

    def get_stock_items(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Retrieve stock items from MYOB EXO"""
        try:
            url = f"{self.base_url}/stockitem"
            if filters:
                params = "&".join([f"{k}={v}" for k, v in filters.items()])
                url += f"?{params}"
            
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to retrieve stock items: {e}")
            return []
    
    def get_stock_report(self, stockcode: str) -> Dict[str, Any]:
        """Get detailed stock report for a specific item"""
        try:
            url = f"{self.base_url}/stockitem/{stockcode}/report"
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to retrieve stock report for {stockcode}: {e}")
            return {}
    
    def analyze_stock_levels(self, include_all: bool = False) -> List[StockAnalysis]:
        """Analyze current stock levels and identify items needing reorder"""
        # Use adaptive stock retrieval based on discovered endpoints
        stock_items = self.get_adaptive_stock_items()
        if not stock_items:
            logger.warning("No stock items retrieved")
            return []
        
        # Filter items that need attention
        items_to_analyze = []
        for item in stock_items:
            try:
                current_stock = float(item.get('qtyonhand', 0))
                reorder_level = float(item.get('reorderlevel', 0))
                
                # Only include items that are at or below reorder level or if include_all is True
                if include_all or current_stock <= reorder_level:
                    items_to_analyze.append(item)
            except Exception as e:
                logger.error(f"Error filtering stock item {item.get('stockcode', 'unknown')}: {e}")
                continue
        
        logger.info(f"Analyzing {len(items_to_analyze)} stock items using batch processing")
        
        # Use batch processing to reduce API calls
        stock_analyses = self.batch_analyze_stock_items(items_to_analyze)
        
        # Sort by priority and days until stockout
        priority_order = {PriorityLevel.CRITICAL: 0, PriorityLevel.HIGH: 1, PriorityLevel.MEDIUM: 2, PriorityLevel.LOW: 3}
        stock_analyses.sort(key=lambda x: (priority_order[x.priority], x.days_until_stockout))
        
        return stock_analyses
    
    def _adjust_priority_with_ai(self, current_priority: PriorityLevel, ai_insights: str) -> PriorityLevel:
        """Adjust priority based on AI insights"""
        try:
            messages = [
                {"role": "system", "content": """You are an expert in inventory prioritization. 
                Based on comprehensive stock analysis, determine if the current priority should be adjusted.
                Return only: CRITICAL, HIGH, MEDIUM, or LOW"""},
                {"role": "user", "content": f"""
                Current priority: {current_priority.value}
                AI Insights: {ai_insights}
                
                Should the priority be adjusted? Return only the priority level.
                """}
            ]
            
            ai_response = self.call_mistral_api(messages)
            
            # Parse AI response to priority level
            ai_priority = ai_response.strip().upper()
            if ai_priority in ["CRITICAL", "HIGH", "MEDIUM", "LOW"]:
                return PriorityLevel(ai_priority.lower())
            
        except Exception as e:
            logger.debug(f"Could not adjust priority with AI: {e}")
        
        return current_priority
    
    def estimate_daily_usage(self, stockcode: str) -> float:
        """Estimate daily usage for a stock item based on historical data"""
        # This is a simplified estimation - in production, you'd use historical sales data
        # For now, we'll use a basic calculation
        try:
            # Try to get historical context from memory
            context = self.memory_client.query_memory([f"stock usage {stockcode}"], n_results=3)
            if context:
                # Use AI to analyze historical usage patterns
                messages = [
                    {"role": "system", "content": "You are an expert in inventory management. Analyze the provided data and estimate daily usage."},
                    {"role": "user", "content": f"Analyze this stock usage data for {stockcode} and provide estimated daily usage as a single number: {context}"}
                ]
                
                ai_response = self.call_mistral_api(messages)
                # Extract number from response
                import re
                numbers = re.findall(r'\d+\.?\d*', ai_response)
                if numbers:
                    return float(numbers[0])
            
            # Fallback to default estimation
            return 1.0
        except Exception as e:
            logger.error(f"Error estimating daily usage for {stockcode}: {e}")
            return 1.0
    
    def forecast_demand(self, stockcode: str, days_ahead: int = 30) -> float:
        """Forecast demand for a stock item using AI analysis"""
        try:
            # Get historical context
            context = self.memory_client.query_memory([f"demand forecast {stockcode}"], n_results=5)
            
            # Get seasonal and trend data
            seasonal_context = self.memory_client.query_memory([f"seasonal trends {stockcode}"], n_results=3)
            
            messages = [
                {"role": "system", "content": """You are an expert demand forecasting analyst. 
                Analyze historical data and provide accurate demand forecasts considering:
                - Seasonal patterns
                - Trend analysis
                - Market conditions
                - Lead times
                Return only a numeric forecast value."""},
                {"role": "user", "content": f"""
                Forecast demand for stock item {stockcode} for the next {days_ahead} days.
                
                Historical context: {context}
                Seasonal context: {seasonal_context}
                
                Consider current market conditions and provide a realistic forecast number.
                """}
            ]
            
            ai_response = self.call_mistral_api(messages)
            
            # Extract forecast number
            import re
            numbers = re.findall(r'\d+\.?\d*', ai_response)
            if numbers:
                return float(numbers[0])
            
            # Fallback
            return 10.0
        except Exception as e:
            logger.error(f"Error forecasting demand for {stockcode}: {e}")
            return 10.0
    
    def generate_purchase_order_recommendations(self, stock_analysis: List[StockAnalysis]) -> List[PurchaseOrderRecommendation]:
        """Generate purchase order recommendations grouped by supplier"""
        if not stock_analysis:
            return []
        
        # Group by supplier
        supplier_groups = {}
        for item in stock_analysis:
            supplier_id = item.supplier_id or "unknown"
            if supplier_id not in supplier_groups:
                supplier_groups[supplier_id] = []
            supplier_groups[supplier_id].append(item)
        
        recommendations = []
        
        for supplier_id, items in supplier_groups.items():
            try:
                # Calculate recommended quantities using AI
                items_data = [asdict(item) for item in items]
                
                messages = [
                    {"role": "system", "content": """You are an expert procurement analyst. 
                    Analyze stock requirements and optimize purchase order quantities considering:
                    - Minimum order quantities
                    - Volume discounts
                    - Lead times
                    - Storage costs
                    - Cash flow optimization
                    """},
                    {"role": "user", "content": f"""
                    Analyze these stock items and optimize purchase order quantities:
                    {json.dumps(items_data, indent=2, cls=EnumEncoder)}
                    
                    For each item, consider:
                    1. Current stock levels
                    2. Reorder levels and quantities
                    3. Forecasted demand
                    4. Lead times
                    5. Priority levels
                    
                    Provide optimized order quantities and justification.
                    """}
                ]
                
                ai_analysis = self.call_mistral_api(messages)
                
                # Calculate totals
                total_value = sum(item.recommended_order_qty * item.cost_price for item in items)
                
                # Determine overall priority
                priorities = [item.priority for item in items]
                if PriorityLevel.CRITICAL in priorities:
                    overall_priority = PriorityLevel.CRITICAL
                elif PriorityLevel.HIGH in priorities:
                    overall_priority = PriorityLevel.HIGH
                elif PriorityLevel.MEDIUM in priorities:
                    overall_priority = PriorityLevel.MEDIUM
                else:
                    overall_priority = PriorityLevel.LOW
                
                # Calculate expected delivery date
                max_lead_time = max(item.lead_time_days for item in items)
                expected_delivery = (datetime.now() + timedelta(days=max_lead_time)).strftime("%Y-%m-%d")
                
                # Generate risk assessment
                risk_messages = [
                    {"role": "system", "content": "You are a supply chain risk analyst. Assess risks for purchase orders."},
                    {"role": "user", "content": f"""
                    Assess supply chain risks for this purchase order:
                    Supplier: {items[0].supplier_name}
                    Total Value: ${total_value:.2f}
                    Items: {len(items)}
                    Priority: {overall_priority.value}
                    Lead Time: {max_lead_time} days
                    
                    Provide a concise risk assessment.
                    """}
                ]
                
                risk_assessment = self.call_mistral_api(risk_messages)
                
                line_items = []
                for item in items:
                    line_items.append({
                        "stockcode": item.stockcode,
                        "description": item.description,
                        "current_stock": item.current_stock,
                        "recommended_qty": item.recommended_order_qty,
                        "cost_price": item.cost_price,
                        "total_cost": item.recommended_order_qty * item.cost_price,
                        "priority": item.priority.value,
                        "days_until_stockout": item.days_until_stockout
                    })
                
                recommendation = PurchaseOrderRecommendation(
                    supplier_id=supplier_id,
                    supplier_name=items[0].supplier_name,
                    total_value=total_value,
                    line_items=line_items,
                    priority=overall_priority,
                    justification=ai_analysis,
                    expected_delivery_date=expected_delivery,
                    risk_assessment=risk_assessment
                )
                
                recommendations.append(recommendation)
                
            except Exception as e:
                logger.error(f"Error generating recommendation for supplier {supplier_id}: {e}")
                continue
        
        # Sort by priority and value
        priority_order = {PriorityLevel.CRITICAL: 0, PriorityLevel.HIGH: 1, PriorityLevel.MEDIUM: 2, PriorityLevel.LOW: 3}
        recommendations.sort(key=lambda x: (priority_order[x.priority], -x.total_value))
        
        return recommendations
    
    def generate_implementation_plan(self, recommendations: List[PurchaseOrderRecommendation]) -> str:
        """Generate a comprehensive implementation plan using AI analysis"""
        if not recommendations:
            return "No purchase order recommendations available."
        
        # Prepare data for AI analysis
        plan_data = {
            "total_recommendations": len(recommendations),
            "total_value": sum(rec.total_value for rec in recommendations),
            "critical_orders": len([rec for rec in recommendations if rec.priority == PriorityLevel.CRITICAL]),
            "high_priority_orders": len([rec for rec in recommendations if rec.priority == PriorityLevel.HIGH]),
            "recommendations": [asdict(rec) for rec in recommendations]
        }
        
        messages = [
            {"role": "system", "content": """You are an expert procurement strategist and implementation specialist. 
            Create comprehensive, actionable implementation plans for purchase order management.
            Your plans should include:
            - Prioritized action steps
            - Timeline recommendations
            - Resource allocation
            - Risk mitigation strategies
            - Budget considerations
            - Performance metrics
            - Contingency plans
            """},
            {"role": "user", "content": f"""
            Create a detailed implementation plan for these purchase order recommendations:
            
            {json.dumps(plan_data, indent=2, cls=EnumEncoder)}
            
            The plan should be:
            1. Actionable and specific
            2. Prioritized by urgency and impact
            3. Include timeline recommendations
            4. Address budget and cash flow considerations
            5. Include risk mitigation strategies
            6. Provide performance metrics to track success
            7. Include contingency planning
            
            Format as a structured implementation plan with clear sections.
            """}
        ]
        
        try:
            implementation_plan = self.call_mistral_api(messages, temperature=0.2)
            
            # Store the plan in memory for future reference
            self.memory_client.add_memory(
                documents=[implementation_plan],
                metadatas=[{
                    "type": "implementation_plan",
                    "date": datetime.now().isoformat(),
                    "total_value": plan_data["total_value"],
                    "recommendations_count": len(recommendations)
                }]
            )
            
            return implementation_plan
            
        except Exception as e:
            logger.error(f"Error generating implementation plan: {e}")
            return f"Error generating implementation plan: {e}"
    
    def run_full_analysis(self) -> Dict[str, Any]:
        """Run complete stock analysis and generate purchase order recommendations"""
        logger.info("Starting full stock analysis and purchase order generation")
        
        try:
            # Step 1: Analyze stock levels using discovered endpoints
            stock_analysis = self.analyze_stock_levels()
            logger.info(f"Analyzed {len(stock_analysis)} stock items requiring attention")
            
            # Step 2: Generate purchase order recommendations
            recommendations = self.generate_purchase_order_recommendations(stock_analysis)
            logger.info(f"Generated {len(recommendations)} purchase order recommendations")
            
            # Step 3: Generate implementation plan
            implementation_plan = self.generate_implementation_plan(recommendations)
            
            # Step 4: Create dynamic workflow based on discovered capabilities
            dynamic_workflow = self.create_dynamic_purchase_order_workflow(recommendations)
            
            # Step 5: Compile results
            results = {
                "analysis_date": datetime.now().isoformat(),
                "discovery_summary": {
                    "total_endpoints": len(self.available_endpoints.get('endpoints', [])),
                    "stock_endpoints": len(self.categorized_endpoints.get('stock_management', [])),
                    "purchase_order_endpoints": len(self.categorized_endpoints.get('purchase_orders', [])),
                    "supplier_endpoints": len(self.categorized_endpoints.get('suppliers', [])),
                    "can_create_purchase_orders": self.purchase_order_capabilities.get("can_create_purchase_orders", False),
                    "can_validate_purchase_orders": self.purchase_order_capabilities.get("can_validate_purchase_orders", False)
                },
                "stock_analysis": [asdict(item) for item in stock_analysis],
                "recommendations": [asdict(rec) for rec in recommendations],
                "implementation_plan": implementation_plan,
                "dynamic_workflow": dynamic_workflow,
                "discovered_capabilities": self.purchase_order_capabilities,
                "summary": {
                    "total_items_analyzed": len(stock_analysis),
                    "total_recommendations": len(recommendations),
                    "total_value": sum(rec.total_value for rec in recommendations),
                    "critical_items": len([item for item in stock_analysis if item.priority == PriorityLevel.CRITICAL]),
                    "high_priority_items": len([item for item in stock_analysis if item.priority == PriorityLevel.HIGH])
                }
            }
            
            # Store results in memory
            self.memory_client.add_memory(
                documents=[json.dumps(results, indent=2, cls=EnumEncoder)],
                metadatas=[{
                    "type": "full_analysis_results",
                    "date": datetime.now().isoformat(),
                    "total_value": results["summary"]["total_value"],
                    "endpoints_discovered": results["discovery_summary"]["total_endpoints"]
                }]
            )
            
            logger.info("Full analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Error in full analysis: {e}")
            raise

    def get_adaptive_stock_items(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Retrieve stock items using the best available endpoints discovered"""
        stock_items = []
        
        # Try to use discovered stock endpoints first
        if self.categorized_endpoints.get("stock_management"):
            for endpoint_info in self.categorized_endpoints["stock_management"]:
                try:
                    if isinstance(endpoint_info, dict):
                        endpoint_url = endpoint_info.get('href', '')
                        endpoint_title = endpoint_info.get('title', '')
                        
                        # Skip report endpoints for bulk retrieval
                        if 'report' in endpoint_title.lower():
                            continue
                        
                        # Try to get stock items from this endpoint
                        url = endpoint_url
                        if filters:
                            params = "&".join([f"{k}={v}" for k, v in filters.items()])
                            url += f"?{params}"
                        
                        response = requests.get(url, headers=self.headers, timeout=30)
                        if response.status_code == 200:
                            data = response.json()
                            
                            # Handle different response formats
                            if isinstance(data, list):
                                stock_items.extend(data)
                            elif isinstance(data, dict) and 'items' in data:
                                stock_items.extend(data['items'])
                            elif isinstance(data, dict) and 'stockitems' in data:
                                stock_items.extend(data['stockitems'])
                            else:
                                stock_items.append(data)
                            
                            logger.info(f"Retrieved {len(data) if isinstance(data, list) else 1} items from {endpoint_title}")
                            break  # Use first successful endpoint
                            
                except Exception as e:
                    logger.debug(f"Could not retrieve from {endpoint_info}: {e}")
                    continue
        
        # Fallback to standard endpoint if discovery failed
        if not stock_items:
            logger.info("Using fallback stock retrieval method")
            return self.get_stock_items(filters)
        
        return stock_items

    def get_enhanced_stock_analysis(self, stockcode: str) -> Dict[str, Any]:
        """Get comprehensive stock analysis using all available endpoints"""
        analysis = {
            "basic_info": {},
            "reports": {},
            "extended_data": {},
            "ai_insights": ""
        }
        
        # Get basic stock information
        analysis["basic_info"] = self.get_stock_report(stockcode)
        
        # Get enhanced data using discovered endpoints
        analysis["extended_data"] = self.get_enhanced_stock_data(stockcode)
        
        # Get data from report endpoints
        if self.categorized_endpoints.get("reports"):
            for endpoint_info in self.categorized_endpoints["reports"]:
                try:
                    if isinstance(endpoint_info, dict):
                        endpoint_url = endpoint_info.get('href', '')
                        endpoint_title = endpoint_info.get('title', '')
                        
                        if 'stock' in endpoint_title.lower() and '{stockcode}' in endpoint_url:
                            url = endpoint_url.replace('{stockcode}', stockcode)
                            response = requests.get(url, headers=self.headers, timeout=30)
                            
                            if response.status_code == 200:
                                report_data = response.json()
                                analysis["reports"][endpoint_title] = report_data
                                
                except Exception as e:
                    logger.debug(f"Could not get report from {endpoint_info}: {e}")
                    continue
        
        # Use AI to analyze all collected data
        messages = [
            {"role": "system", "content": """You are an expert inventory analyst. 
            Analyze all available stock data and provide comprehensive insights for purchase order decisions."""},
            {"role": "user", "content": f"""
            Analyze this comprehensive stock data for item {stockcode}:
            
            Basic Info: {json.dumps(analysis["basic_info"], indent=2)}
            Extended Data: {json.dumps(analysis["extended_data"], indent=2)}
            Reports: {json.dumps(analysis["reports"], indent=2)}
            
            Provide insights on:
            1. Current stock situation
            2. Usage patterns and trends
            3. Reorder recommendations
            4. Risk factors
            5. Seasonal considerations
            
            Format as a structured analysis with actionable recommendations.
            """}
        ]
        
        try:
            ai_insights = self.call_mistral_api(messages)
            analysis["ai_insights"] = ai_insights
        except Exception as e:
            logger.error(f"Error generating AI insights: {e}")
            analysis["ai_insights"] = "AI analysis unavailable"
        
        return analysis

    def create_dynamic_purchase_order_workflow(self, recommendations: List[PurchaseOrderRecommendation]) -> Dict[str, Any]:
        """Create a dynamic purchase order workflow based on discovered capabilities"""
        workflow = {
            "workflow_steps": [],
            "required_data": [],
            "validation_steps": [],
            "creation_endpoints": [],
            "fallback_options": [],
            "estimated_completion_time": "",
            "ai_workflow_optimization": ""
        }
        
        # Analyze available capabilities
        can_create = self.purchase_order_capabilities.get("can_create_purchase_orders", False)
        can_validate = self.purchase_order_capabilities.get("can_validate_purchase_orders", False)
        can_search = self.purchase_order_capabilities.get("can_search_purchase_orders", False)
        
        # Build workflow steps
        if can_validate:
            workflow["workflow_steps"].append({
                "step": "validate_purchase_orders",
                "description": "Validate purchase order data before creation",
                "endpoints": [ep for ep in self.purchase_order_capabilities.get("available_purchase_endpoints", []) 
                            if 'validate' in ep.get('title', '').lower()]
            })
        
        if can_create:
            workflow["workflow_steps"].append({
                "step": "create_purchase_orders",
                "description": "Create purchase orders in MYOB EXO",
                "endpoints": [ep for ep in self.purchase_order_capabilities.get("available_purchase_endpoints", []) 
                            if 'create' in ep.get('title', '').lower() or ep.get('href', '').endswith('/purchaseorder')]
            })
        
        if can_search:
            workflow["workflow_steps"].append({
                "step": "verify_creation",
                "description": "Search and verify created purchase orders",
                "endpoints": [ep for ep in self.purchase_order_capabilities.get("available_purchase_endpoints", []) 
                            if 'search' in ep.get('title', '').lower()]
            })
        
        # Use AI to optimize workflow
        messages = [
            {"role": "system", "content": """You are an expert in ERP workflow optimization. 
            Analyze the available capabilities and create an optimized purchase order workflow."""},
            {"role": "user", "content": f"""
            Based on these MYOB EXO capabilities and purchase order recommendations:
            
            Capabilities: {json.dumps(self.purchase_order_capabilities, indent=2)}
            Recommendations: {json.dumps([asdict(rec) for rec in recommendations], indent=2, cls=EnumEncoder)}
            
            Create an optimized workflow that:
            1. Maximizes success rate
            2. Minimizes manual intervention
            3. Provides clear error handling
            4. Includes validation steps
            5. Estimates completion time
            6. Provides fallback options
            
            Format as a structured workflow plan.
            """}
        ]
        
        try:
            ai_optimization = self.call_mistral_api(messages)
            workflow["ai_workflow_optimization"] = ai_optimization
        except Exception as e:
            logger.error(f"Error generating workflow optimization: {e}")
            workflow["ai_workflow_optimization"] = "AI optimization unavailable"
        
        return workflow

    def batch_analyze_stock_items(self, stock_items: List[Dict[str, Any]]) -> List[StockAnalysis]:
        """Analyze multiple stock items in batches to reduce API calls"""
        if not stock_items:
            return []
        
        # Process in batches of 5-10 items to avoid overwhelming the API
        batch_size = 5
        all_analyses = []
        
        for i in range(0, len(stock_items), batch_size):
            batch = stock_items[i:i + batch_size]
            
            try:
                # Prepare batch analysis prompt
                batch_data = []
                for item in batch:
                    batch_data.append({
                        "stockcode": item.get("stockcode", ""),
                        "description": item.get("description", ""),
                        "current_stock": item.get("qtyonhand", 0),
                        "reorder_level": item.get("reorderlevel", 0),
                        "reorder_quantity": item.get("reorderqty", 0),
                        "cost_price": item.get("costprice", 0)
                    })
                
                messages = [
                    {"role": "system", "content": """You are an expert inventory analyst. Analyze the provided stock items and return a JSON array with analysis for each item.
                    For each item, provide:
                    - priority (critical/high/medium/low)
                    - forecast_demand (numeric estimate for 30 days)
                    - recommended_order_qty (numeric)
                    - days_until_stockout (numeric estimate)
                    - seasonal_factor (numeric, default 1.0)
                    - risk_assessment (brief text)
                    
                    Return only valid JSON array."""},
                    {"role": "user", "content": f"""Analyze these stock items in batch:
                    {json.dumps(batch_data, indent=2)}
                    
                    Return analysis as JSON array matching the input order."""}
                ]
                
                ai_response = self.call_mistral_api(messages, temperature=0.2)
                
                # Parse AI response
                try:
                    batch_analyses = json.loads(ai_response)
                    
                    # Convert to StockAnalysis objects
                    for j, analysis in enumerate(batch_analyses):
                        if j < len(batch):
                            item = batch[j]
                            try:
                                priority = PriorityLevel(analysis.get("priority", "medium").lower())
                                
                                stock_analysis = StockAnalysis(
                                    stockcode=item.get("stockcode", ""),
                                    description=item.get("description", ""),
                                    current_stock=float(item.get("qtyonhand", 0)),
                                    reorder_level=float(item.get("reorderlevel", 0)),
                                    reorder_quantity=float(item.get("reorderqty", 0)),
                                    cost_price=float(item.get("costprice", 0)),
                                    supplier_id=item.get("supplierid", ""),
                                    supplier_name=item.get("suppliername", ""),
                                    lead_time_days=int(item.get("leadtime", 7)),
                                    priority=priority,
                                    forecast_demand=float(analysis.get("forecast_demand", 10)),
                                    recommended_order_qty=float(analysis.get("recommended_order_qty", 0)),
                                    days_until_stockout=int(analysis.get("days_until_stockout", 30)),
                                    seasonal_factor=float(analysis.get("seasonal_factor", 1.0))
                                )
                                all_analyses.append(stock_analysis)
                                
                            except Exception as e:
                                logger.error(f"Error processing analysis for item {j}: {e}")
                                # Add basic analysis as fallback
                                all_analyses.append(self._create_basic_analysis(item))
                
                except json.JSONDecodeError:
                    logger.warning("AI response was not valid JSON, falling back to individual analysis")
                    # Fallback to individual analysis
                    for item in batch:
                        all_analyses.append(self._create_basic_analysis(item))
                
                # Add delay between batches
                if i + batch_size < len(stock_items):
                    logger.info(f"Processed batch {i//batch_size + 1}, sleeping before next batch...")
                    time.sleep(3)  # 3 second delay between batches
                    
            except Exception as e:
                logger.error(f"Error processing batch starting at index {i}: {e}")
                # Fallback to basic analysis for this batch
                for item in batch:
                    all_analyses.append(self._create_basic_analysis(item))
        
        return all_analyses
    
    def _create_basic_analysis(self, item: Dict[str, Any]) -> StockAnalysis:
        """Create basic stock analysis without AI when API fails"""
        current_stock = float(item.get("qtyonhand", 0))
        reorder_level = float(item.get("reorderlevel", 0))
        
        # Basic priority calculation
        if current_stock <= 0:
            priority = PriorityLevel.CRITICAL
        elif current_stock <= reorder_level * 0.5:
            priority = PriorityLevel.HIGH
        elif current_stock <= reorder_level:
            priority = PriorityLevel.MEDIUM
        else:
            priority = PriorityLevel.LOW
        
        # Basic calculations
        daily_usage = max(1.0, reorder_level / 30)  # Estimate based on reorder level
        days_until_stockout = max(1, int(current_stock / daily_usage)) if daily_usage > 0 else 30
        
        return StockAnalysis(
            stockcode=item.get("stockcode", ""),
            description=item.get("description", ""),
            current_stock=current_stock,
            reorder_level=reorder_level,
            reorder_quantity=float(item.get("reorderqty", 0)),
            cost_price=float(item.get("costprice", 0)),
            supplier_id=item.get("supplierid", ""),
            supplier_name=item.get("suppliername", ""),
            lead_time_days=int(item.get("leadtime", 7)),
            priority=priority,
            forecast_demand=daily_usage * 30,
            recommended_order_qty=float(item.get("reorderqty", 0)),
            days_until_stockout=days_until_stockout,
            seasonal_factor=1.0
        )
    
    # ... existing methods continue below ...
    
# Example usage and testing functions
def main():
    """Main function to demonstrate the agent's capabilities"""
    try:
        # Initialize the agent
        agent = MyobPurchaseOrderAgent()
        
        # Run full analysis
        results = agent.run_full_analysis()
        
        # Print discovery summary
        print("\n" + "="*80)
        print("MYOB EXO ENDPOINT DISCOVERY SUMMARY")
        print("="*80)
        discovery = results['discovery_summary']
        print(f"Total Endpoints Discovered: {discovery['total_endpoints']}")
        print(f"Stock Management Endpoints: {discovery['stock_endpoints']}")
        print(f"Purchase Order Endpoints: {discovery['purchase_order_endpoints']}")
        print(f"Supplier Endpoints: {discovery['supplier_endpoints']}")
        print(f"Can Create Purchase Orders: {discovery['can_create_purchase_orders']}")
        print(f"Can Validate Purchase Orders: {discovery['can_validate_purchase_orders']}")
        
        # Print analysis summary
        print("\n" + "="*80)
        print("MYOB PURCHASE ORDER ANALYSIS RESULTS")
        print("="*80)
        print(f"Analysis Date: {results['analysis_date']}")
        print(f"Total Items Analyzed: {results['summary']['total_items_analyzed']}")
        print(f"Total Recommendations: {results['summary']['total_recommendations']}")
        print(f"Total Value: ${results['summary']['total_value']:,.2f}")
        print(f"Critical Items: {results['summary']['critical_items']}")
        print(f"High Priority Items: {results['summary']['high_priority_items']}")
        
        # Print dynamic workflow information
        print("\n" + "="*80)
        print("DYNAMIC WORKFLOW SUMMARY")
        print("="*80)
        workflow = results['dynamic_workflow']
        print(f"Workflow Steps: {len(workflow['workflow_steps'])}")
        for i, step in enumerate(workflow['workflow_steps'], 1):
            print(f"  {i}. {step['description']}")
        
        print("\n" + "="*80)
        print("IMPLEMENTATION PLAN")
        print("="*80)
        print(results['implementation_plan'])
        
        # Print AI workflow optimization if available
        if workflow.get('ai_workflow_optimization'):
            print("\n" + "="*80)
            print("AI WORKFLOW OPTIMIZATION")
            print("="*80)
            print(workflow['ai_workflow_optimization'])
        
        return results
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise

if __name__ == "__main__":
    main()
