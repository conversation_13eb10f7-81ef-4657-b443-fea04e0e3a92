"""Pydantic models for LLM service."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class CustomerDetails(BaseModel):
    """Customer details model."""
    
    debtor_id: int = Field(..., ge=0, description="Customer debtor ID (0 for unknown)")
    customer_order_number: Optional[str] = Field(None, description="Customer's PO number")
    customer_name: Optional[str] = Field(None, description="Customer company name")
    delivery_address: Optional[str] = Field(None, description="Delivery address")
    shipping_method: Optional[str] = Field(None, description="Shipping method")


class DeliveryAddress(BaseModel):
    """Delivery address model."""
    
    line1: Optional[str] = None
    line2: Optional[str] = None
    line3: Optional[str] = None
    line4: Optional[str] = None
    line5: Optional[str] = None
    line6: Optional[str] = None


class OrderLine(BaseModel):
    """Order line item model."""
    
    stockcode: str = Field(..., description="Product stock code")
    orderquantity: float = Field(..., description="Quantity ordered")
    
    @validator('stockcode')
    def validate_stockcode(cls, v):
        if not v or not v.strip():
            raise ValueError("stockcode cannot be empty")
        return v.strip()
    
    @validator('orderquantity')
    def validate_quantity(cls, v):
        if v < 0:
            raise ValueError("orderquantity must be non-negative")
        return v


class ExtractedOrder(BaseModel):
    """Extracted order data model."""
    
    customer_details: CustomerDetails
    order_lines: List[OrderLine]
    delivery_address: Optional[DeliveryAddress] = None
    X_SHIPVIA: Optional[str] = None
    order_status: int = 0
    
    @validator('order_lines')
    def validate_order_lines(cls, v):
        if not v:
            raise ValueError("order_lines cannot be empty")
        return v


class EmailSummary(BaseModel):
    """Email summary model."""
    
    summary: str = Field(..., description="Brief summary of email")
    action_required: str = Field(..., description="Required action")


class MYOBPayload(BaseModel):
    """MYOB-compatible payload model."""
    
    debtorid: int
    customerordernumber: Optional[str] = None
    status: int = 0
    defaultlocationid: int = 1
    lines: List[Dict[str, Any]]
    deliveryaddress: Optional[Dict[str, Optional[str]]] = None
    extrafields: Optional[List[Dict[str, str]]] = None
    
    @validator('lines')
    def validate_lines(cls, v):
        if not v:
            raise ValueError("lines cannot be empty")
        for line in v:
            if 'stockcode' not in line or 'orderquantity' not in line:
                raise ValueError("Each line must have stockcode and orderquantity")
        return v


class EmailData(BaseModel):
    """Email data model."""
    
    subject: str
    sender: str
    body: str
    id: Optional[str] = None


class EmailAnalysis(BaseModel):
    """Email analysis result model."""
    
    is_order: bool = Field(..., description="Whether the email contains an order")
    confidence: float = Field(..., ge=0, le=1, description="Confidence score")
    order_type: Optional[str] = Field(None, description="Type of order identified")
    summary: str = Field(..., description="Summary of the email content")


class OrderData(BaseModel):
    """Complete order data model."""
    
    customer_details: CustomerDetails
    purchase_order_number: Optional[str] = None
    line_items: List[Dict[str, Any]]
    delivery_address: Optional[DeliveryAddress] = None
    special_instructions: Optional[str] = None
    total_amount: Optional[float] = None
    currency: str = Field(default="AUD", description="Currency code")