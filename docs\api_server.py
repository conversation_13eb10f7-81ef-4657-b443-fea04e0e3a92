"""
Flask API for Multi-Agent System
Provides REST API endpoints for email processing and system management.
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
from dotenv import load_dotenv

# Import the orchestrator and agents
try:
    from test_email_agent import MockEmailAgent
    from test_myob_agent import MockMY<PERSON><PERSON>gent
    
    # Create mock orchestrator for testing
    class MockOrchestrator:
        def __init__(self):
            self.email_agent = MockEmailAgent()
            self.myob_agent = MockMYOBAgent()
            self.running = True
        
        def process_email_manually(self, email_data):
            result = self.email_agent.process_email(email_data)
            if email_data.get('generate_reply') and result.get('success'):
                result['suggested_reply'] = self.email_agent.generate_reply(
                    result['tone'], 
                    result['intent'],
                    email_data['sender'].split('@')[0].replace('.', ' ').title()
                )
            return result
        
        def get_system_status(self):
            return {
                "system_running": self.running,
                "pending_orders_count": 0,
                "recent_activity": [],
                "agents": {
                    "email_agent": {"status": "active"},
                    "myob_agent": {"status": "active"}
                }
            }
    
    orchestrator = MockOrchestrator()
    
except ImportError:
    # Fallback if real orchestrator is not available
    class MockOrchestrator:
        def process_email_manually(self, email_data):
            return {"success": False, "error": "System not fully initialized"}
        
        def get_system_status(self):
            return {"error": "System not available"}
    
    orchestrator = MockOrchestrator()

# Load environment variables
load_dotenv()

# Create Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# HTML template for the dashboard
DASHBOARD_HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Agent AI System Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-active { background-color: #4CAF50; }
        .status-inactive { background-color: #f44336; }
        .btn { background-color: #667eea; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background-color: #5a6fd8; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .form-group textarea { height: 100px; resize: vertical; }
        .result { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 15px; border-left: 4px solid #667eea; }
        .error { border-left-color: #f44336; background-color: #ffebee; }
        .success { border-left-color: #4CAF50; background-color: #e8f5e9; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { .grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Multi-Agent AI System</h1>
            <p>Sales Email Processing & Purchase Order Automation</p>
        </div>

        <div class="grid">
            <div class="card">
                <h2>📊 System Status</h2>
                <div id="system-status">
                    <p><span class="status-indicator status-active"></span>Email Agent: Active</p>
                    <p><span class="status-indicator status-active"></span>MYOB Agent: Active</p>
                    <p><span class="status-indicator status-active"></span>System: Running</p>
                </div>
                <button class="btn" onclick="refreshStatus()">Refresh Status</button>
            </div>

            <div class="card">
                <h2>📈 Quick Stats</h2>
                <div id="quick-stats">
                    <p>Pending Orders: <span id="pending-count">-</span></p>
                    <p>Recent Activity: <span id="recent-activity">-</span></p>
                    <p>Last Update: <span id="last-update">-</span></p>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>📧 Process Email</h2>
            <form id="email-form">
                <div class="form-group">
                    <label for="sender">Sender Email:</label>
                    <input type="email" id="sender" name="sender" required placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="subject">Subject:</label>
                    <input type="text" id="subject" name="subject" required placeholder="Purchase Order Request">
                </div>
                <div class="form-group">
                    <label for="body">Email Body:</label>
                    <textarea id="body" name="body" required placeholder="Enter email content here..."></textarea>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="generate-reply" name="generate_reply" checked>
                        Generate suggested reply
                    </label>
                </div>
                <button type="submit" class="btn">Process Email</button>
                <button type="button" class="btn" onclick="loadSampleEmail()">Load Sample</button>
            </form>
            <div id="email-result"></div>
        </div>

        <div class="card">
            <h2>🧪 Run Integration Tests</h2>
            <p>Test the complete email-to-MYOB workflow with predefined test cases.</p>
            <button class="btn" onclick="runTests()">Run All Tests</button>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        async function refreshStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                document.getElementById('pending-count').textContent = data.pending_orders_count || 0;
                document.getElementById('recent-activity').textContent = data.recent_activity?.length || 0;
                document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
            } catch (error) {
                console.error('Error refreshing status:', error);
            }
        }

        async function processEmail() {
            const form = document.getElementById('email-form');
            const formData = new FormData(form);
            
            const emailData = {
                sender: formData.get('sender'),
                recipient: '<EMAIL>',
                subject: formData.get('subject'),
                body_plain: formData.get('body'),
                generate_reply: formData.has('generate_reply'),
                attachments: []
            };

            try {
                const response = await fetch('/api/process-email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(emailData)
                });

                const result = await response.json();
                displayEmailResult(result);
            } catch (error) {
                displayEmailResult({ success: false, error: error.message });
            }
        }

        function displayEmailResult(result) {
            const resultDiv = document.getElementById('email-result');
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ Email Processed Successfully</h3>
                        <p><strong>Tone:</strong> ${result.tone}</p>
                        <p><strong>Intent:</strong> ${result.intent}</p>
                        <p><strong>Attachments:</strong> ${result.attachments?.length || 0}</p>
                        ${result.suggested_reply ? `
                            <h4>Suggested Reply:</h4>
                            <div style="background: #f0f0f0; padding: 10px; border-radius: 5px; white-space: pre-wrap;">${result.suggested_reply}</div>
                        ` : ''}
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Processing Failed</h3>
                        <p>${result.error}</p>
                    </div>
                `;
            }
        }

        async function runTests() {
            const button = event.target;
            button.disabled = true;
            button.textContent = 'Running Tests...';
            
            try {
                const response = await fetch('/api/run-tests', { method: 'POST' });
                const result = await response.json();
                
                const resultsDiv = document.getElementById('test-results');
                resultsDiv.innerHTML = `
                    <div class="result ${result.success ? 'success' : 'error'}">
                        <h3>${result.success ? '✅' : '❌'} Integration Tests</h3>
                        <p><strong>Passed:</strong> ${result.passed}/${result.total}</p>
                        <p><strong>Success Rate:</strong> ${result.success_rate}%</p>
                        <details>
                            <summary>View Details</summary>
                            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto;">${result.details}</pre>
                        </details>
                    </div>
                `;
            } catch (error) {
                document.getElementById('test-results').innerHTML = `
                    <div class="result error">
                        <h3>❌ Test Execution Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                button.disabled = false;
                button.textContent = 'Run All Tests';
            }
        }

        function loadSampleEmail() {
            document.getElementById('sender').value = '<EMAIL>';
            document.getElementById('subject').value = 'Purchase Order ABC-2024-001';
            document.getElementById('body').value = `Dear Sales Team,

Please find attached our purchase order for the following items. We need these delivered by the end of next week.

Thank you for your prompt attention to this order.

Best regards,
Sarah Johnson
Procurement Manager
ABC Corporation`;
        }

        document.getElementById('email-form').addEventListener('submit', function(e) {
            e.preventDefault();
            processEmail();
        });

        // Initial status refresh
        refreshStatus();
        setInterval(refreshStatus, 30000); // Refresh every 30 seconds
    </script>
</body>
</html>
"""

@app.route('/')
def dashboard():
    """Serve the main dashboard."""
    return render_template_string(DASHBOARD_HTML)

@app.route('/api/status', methods=['GET'])
def get_system_status():
    """Get current system status."""
    try:
        status = orchestrator.get_system_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/process-email', methods=['POST'])
def process_email():
    """Process a single email through the system."""
    try:
        email_data = request.get_json()
        
        if not email_data:
            return jsonify({"error": "No email data provided"}), 400
        
        # Validate required fields
        required_fields = ['sender', 'recipient', 'subject', 'body_plain']
        for field in required_fields:
            if field not in email_data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Process email
        result = orchestrator.process_email_manually(email_data)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error processing email: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/run-tests', methods=['POST'])
def run_integration_tests():
    """Run integration tests and return results."""
    try:
        # Import and run the integration tests
        from test_integration import run_end_to_end_tests
        
        # Capture test output
        import io
        import sys
        from contextlib import redirect_stdout
        
        output_buffer = io.StringIO()
        
        with redirect_stdout(output_buffer):
            test_results = run_end_to_end_tests()
        
        output = output_buffer.getvalue()
        
        # Calculate summary
        passed_tests = sum(1 for test in test_results if test["passed"])
        total_tests = len(test_results)
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        return jsonify({
            "success": passed_tests == total_tests,
            "passed": passed_tests,
            "total": total_tests,
            "success_rate": round(success_rate, 1),
            "details": output,
            "test_results": test_results
        })
        
    except Exception as e:
        logger.error(f"Error running integration tests: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/orders', methods=['GET'])
def get_orders():
    """Get all orders with their current status."""
    try:
        # This would query the database in a real implementation
        # For now, return mock data
        orders = [
            {
                "id": "order-001",
                "order_number": "PO-ABC-2024-001",
                "customer_name": "ABC Corporation",
                "status": "submitted",
                "total_amount": 7500.00,
                "currency": "USD",
                "created_at": "2024-06-17T10:30:00Z"
            }
        ]
        
        return jsonify({"orders": orders})
        
    except Exception as e:
        logger.error(f"Error getting orders: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/logs', methods=['GET'])
def get_agent_logs():
    """Get recent agent activity logs."""
    try:
        limit = request.args.get('limit', 50, type=int)
        agent_name = request.args.get('agent')
        
        # This would query the database in a real implementation
        logs = []
        try:
            logs = [
                {
                    "id": "log-001",
                    "agent_name": "Email Agent",
                    "log_level": "INFO",
                    "message": "Email processed successfully",
                    "timestamp": datetime.now().isoformat()
                }
            ]
        except:
            pass
        
        return jsonify({"logs": logs})
        
    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    })

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return jsonify({"error": "Internal server error"}), 500

def create_app():
    """Application factory."""
    return app

if __name__ == '__main__':
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 Starting Multi-Agent AI System API Server...")
    print("📊 Dashboard available at: http://localhost:5000")
    print("🔗 API endpoints available at: http://localhost:5000/api/")
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )

