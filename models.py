"""
Data models for the email order processor - Updated to match Supabase schema.
"""
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
import uuid

class CustomerDetails(BaseModel):
    debtor_id: int
    customer_order_number: Optional[str] = None

class DeliveryAddress(BaseModel):
    line1: Optional[str] = None
    line2: Optional[str] = None
    line3: Optional[str] = None
    line4: Optional[str] = None
    line5: Optional[str] = None
    line6: Optional[str] = None

class OrderLine(BaseModel):
    stockcode: str
    orderquantity: float

class ExtractedOrder(BaseModel):
    customer_details: CustomerDetails
    defaultlocationid: int = Field(default=1)
    order_status: int = Field(default=0, description="0=Not Processed, 3=Quotation, 4=Standing Order, 5=Layby")
    delivery_address: Optional[DeliveryAddress] = None
    order_lines: List[OrderLine]
    X_SHIPVIA: Optional[str] = Field(default=None, description="The shipping method")

class EmailAttachment(BaseModel):
    """Model for email attachments - matches Supabase email_attachments table."""
    id: Optional[str] = None  # UUID, auto-generated
    email_id: Optional[str] = None  # UUID reference to emails table
    filename: str
    content_type: Optional[str] = None
    size_bytes: Optional[int] = None
    attachment_id: Optional[str] = None  # Gmail attachment ID
    processed: bool = False
    extracted_text: Optional[str] = None
    data: Optional[bytes] = None  # Raw attachment data (not stored in DB)

class EmailData(BaseModel):
    """Model for email data - Updated to match Supabase emails table schema."""
    # Supabase schema fields
    id: Optional[str] = None  # UUID, auto-generated by Supabase
    gmail_id: str  # Gmail message ID - maps to gmail_id column
    subject: str
    sender: str
    recipient: Optional[str] = None
    body: str
    html_body: Optional[str] = None  # Added for Supabase schema
    thread_id: Optional[str] = None  # Gmail thread ID
    label_ids: List[str] = []  # Gmail labels as array
    received_date: Optional[datetime] = None  # Timestamp when email was received
    processed_date: Optional[datetime] = None  # When email was processed
    category: Optional[str] = None  # Email categorization
    debtor_id: Optional[int] = None  # Link to customers table
    confidence_score: Optional[float] = None  # AI confidence score
    has_attachments: bool = False
    attachment_count: int = 0
    
    # Compatibility fields (deprecated but kept for backward compatibility)
    timestamp: Optional[str] = None  # Deprecated: use received_date
    attachments: List[EmailAttachment] = []  # Will be stored in separate table
    source_label: Optional[str] = None  # Deprecated: use label_ids

class OrderLineItem(BaseModel):
    """Model for order line items - matches Supabase order_line_items table."""
    id: Optional[str] = None  # UUID, auto-generated
    email_id: Optional[str] = None  # UUID reference to emails table
    erp_payload_id: Optional[str] = None  # UUID reference to erp_payloads table
    line_number: Optional[int] = None
    stock_code: Optional[str] = None
    description: Optional[str] = None
    quantity: Optional[float] = None
    unit_price: Optional[float] = None
    line_total: Optional[float] = None
    unit_of_measure: Optional[str] = None

class EmailCategorization(BaseModel):
    """Model for email categorizations - matches Supabase email_categorizations table."""
    id: Optional[str] = None  # UUID, auto-generated
    email_id: Optional[str] = None  # UUID reference to emails table
    category: str
    subcategory: Optional[str] = None
    confidence_score: Optional[float] = None
    extracted_data: Optional[dict] = None  # JSON data from AI processing
    model_used: Optional[str] = None
    processing_time_ms: Optional[int] = None

class ERPPayload(BaseModel):
    """Model for ERP payloads - matches Supabase erp_payloads table."""
    id: Optional[str] = None  # UUID, auto-generated
    email_id: Optional[str] = None  # UUID reference to emails table
    debtor_id: int  # Customer reference, required
    payload_type: str  # Type of payload (e.g., 'order', 'quote')
    payload_data: dict  # JSON payload for MYOB
    status: str = "draft"  # Workflow status
    approval_required: bool = True
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    submitted_at: Optional[datetime] = None
    erp_response: Optional[dict] = None
    customer_order_number: Optional[str] = None
    po_number: Optional[str] = None
    total_amount: Optional[float] = None

class ProcessedOrder(BaseModel):
    """Model for a processed order ready for MYOB - Updated for Supabase integration."""
    email_id: str  # Now refers to Supabase email UUID
    email_subject: str
    email_data: EmailData
    extracted_data: Optional[ExtractedOrder] = None
    markdown_summary: str
    myob_payload: Optional[dict] = None
    markdown_filepath: Optional[str] = None
    myob_filepath: Optional[str] = None
    
    # Supabase integration fields
    erp_payload_id: Optional[str] = None  # Reference to erp_payloads table
    categorization_id: Optional[str] = None  # Reference to email_categorizations table
    order_line_items: List[OrderLineItem] = []  # Associated line items
