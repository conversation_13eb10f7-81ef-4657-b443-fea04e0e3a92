"""
Main email order processor - orchestrates the complete workflow.
Refactored to focus only on workflow orchestration logic.
"""
import logging
import os
import json
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import ValidationError

from config import config
from models import ExtractedOrder, ProcessedOrder, EmailData
from gmail_service import GmailService
from pdf_extractor import extract_text_from_pdf
from llm_service import LLMService
from supabase_database_service import SupabaseService
from email_categorizer import EmailCategorizer, EmailClassification, EmailCategory, EmailPriority

logger = logging.getLogger(__name__)


class EmailOrderProcessor:
    """Main processor that orchestrates the email-to-markdown/JSON workflow with Supabase integration."""
    
    def __init__(self):
        logger.info("Initializing Email Order Processor with intelligent categorization")
        self.gmail_service = GmailService()
        self.db_service = SupabaseService()  # Initialize Supabase service first
        # Pass Mistral API key and Supabase service to LLM service
        self.llm_service = LLMService(config.MISTRAL_API_KEY, self.db_service)
        # Initialize email categorizer for intelligent processing
        self.email_categorizer = EmailCategorizer(self.llm_service)
        self._ensure_directories()
        self._setup_processing_labels()
        logger.info("Email Order Processor with intelligent categorization initialized successfully")
    
    def _setup_processing_labels(self):
        """Setup traffic light processing labels in Gmail."""
        try:
            labels = self.gmail_service.get_or_create_processing_labels()
            if labels:
                logger.info("Traffic light processing labels ready: Processed, Review, Failed")
            else:
                logger.warning("Failed to setup processing labels - labeling will be disabled")
        except Exception as e:
            logger.error(f"Error setting up processing labels: {e}")

    def _ensure_directories(self):
        directories = ["markdown", "myob"]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        logger.info("Created output directories: markdown/, myob/")

    def _sanitize_filename(self, text: str) -> str:
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            text = text.replace(char, '_')
        return text.strip()[:50]

    def _generate_filename_prefix(self, email_subject: str) -> str:
        now = datetime.now()
        date_prefix = now.strftime("%d-%m_%H%M")
        sanitized_subject = self._sanitize_filename(email_subject)
        return f"{date_prefix}_{sanitized_subject}"

    def _save_markdown_file(self, email_subject: str, markdown_content: str) -> str:
        filename_prefix = self._generate_filename_prefix(email_subject)
        filepath = os.path.join("markdown", f"{filename_prefix}.md")
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            logger.info(f"Saved markdown summary to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save markdown file: {e}")
            return ""

    def _save_myob_file(self, email_subject: str, myob_payload: dict) -> str:
        filename_prefix = self._generate_filename_prefix(email_subject)
        filepath = os.path.join("myob", f"{filename_prefix}.json")
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(myob_payload, f, indent=2)
            logger.info(f"Saved MYOB payload to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save MYOB file: {e}")
            return ""

    def _generate_order_id(self) -> str:
        import glob
        pattern = os.path.join("myob", "*.json")
        existing_files = glob.glob(pattern)
        existing_numbers = []
        for filepath in existing_files:
            filename = os.path.basename(filepath)
            if filename.startswith("TS") and filename.endswith(".json"):
                try:
                    number_str = filename[2:-5]
                    existing_numbers.append(int(number_str))
                except ValueError:
                    continue
        next_number = max(existing_numbers, default=0) + 1
        return f"TS{next_number:03d}"

    def _create_simple_email_summary(self, email: EmailData) -> str:
        """Create a simple markdown summary for emails without PDF attachments."""
        summary = f"""# Email Summary - {email.subject}

**Date:** {datetime.now().strftime("%B %d, %Y at %I:%M %p")}
**Email ID:** {email.id}
**Subject:** {email.subject}
**From:** {getattr(email, 'sender', 'Unknown')}
**To:** {getattr(email, 'recipient', 'Unknown')}

## Email Content

{email.body[:1000]}{'...' if len(email.body) > 1000 else ''}

## Attachments

"""
        
        if email.attachments:
            for attachment in email.attachments:
                summary += f"- {attachment['filename']} ({attachment.get('size', 'Unknown size')})\n"
        else:
            summary += "No attachments found.\n"
        
        summary += f"""
## Processing Notes

- **Status:** Marked for Review
- **Reason:** {'No PDF attachments found' if not any(att['filename'].lower().endswith('.pdf') for att in email.attachments) else 'No meaningful content extracted'}
- **Action Required:** Manual review needed to determine if this email contains order information

---
*Generated by TeamsysV0.1 Email Order Processor*
"""
        
        return summary

    def _save_to_supabase(self, processed_order: ProcessedOrder) -> bool:
        """Save processed order to Supabase database."""
        try:
            # Convert the processed order to a format suitable for database storage
            order_data = {
                'email_id': processed_order.email_id,
                'email_subject': processed_order.email_subject,
                'extracted_data': processed_order.extracted_data.model_dump() if processed_order.extracted_data else None,
                'markdown_summary': processed_order.markdown_summary,
                'myob_payload': processed_order.myob_payload,
                'markdown_filepath': processed_order.markdown_filepath,
                'myob_filepath': processed_order.myob_filepath,
                'processed_at': datetime.now().isoformat()
            }
            
            # Save to database
            result = self.db_service.save_processed_order(order_data)
            if result:
                logger.info(f"Successfully saved order to Supabase: {processed_order.email_id}")
                return True
            else:
                logger.error(f"Failed to save order to Supabase: {processed_order.email_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error saving to Supabase: {e}")
            return False

    async def run_extraction_workflow(self, label_names: Optional[List[str]] = None, max_emails: int = 40, 
                      time_filter: Optional[str] = None, review_mode: bool = False) -> List[ProcessedOrder]:
        """
        Main workflow method for email extraction.
        This is the primary entry point that should be called by CLI.
        """
        logger.info("Starting email processing workflow (extraction-only)")
        emails = []
        
        # Use provided labels or default ones
        if label_names is None:
            label_names = ['Brady', 'RSEA', 'Woolworths','Brierley', 'Gateway', 'Highgate', 'Sitecraft']
        
        logger.info(f"Processing emails from labels: {', '.join(label_names)}")
        logger.info(f"Max emails per label: {max_emails}")
        if review_mode:
            logger.info("Review mode enabled - processing ALL emails (not just PDFs)")
        
        # Use the provided query (which may be empty for review_all_emails mode)
        query = time_filter or ""
        if query:
            logger.info(f"Using query filter: {query}")
        else:
            logger.info("No query filter - processing all emails in labels")
            
        for label_name in label_names:
            label_id = self.gmail_service.get_label_id(label_name)
            gmail_service_instance = getattr(self.gmail_service, "service", None)
            if label_id and gmail_service_instance is not None:
                try:
                    # Use query if provided, otherwise get all emails
                    list_params = {
                        'userId': 'me',
                        'labelIds': [label_id],
                        'maxResults': max_emails
                    }
                    if query:
                        list_params['q'] = query
                    
                    response = gmail_service_instance.users().messages().list(**list_params).execute()
                    messages = response.get('messages', [])
                    logger.info(f"Found {len(messages)} emails in label '{label_name}'")
                    for msg_ref in messages:
                        email_data = self.gmail_service._get_email_details(msg_ref['id'], label_name)
                        # Ignore Brady packing slip emails (unless in review mode and specifically included)
                        if (label_name.lower() == 'brady' and email_data and 
                            'packing slip' in email_data.subject.lower() and not review_mode):
                            logger.info(f"Ignoring Brady packing slip email: {email_data.subject}")
                            logger.info(f"Skipping Brady packing slip labeling for: {email_data.id}")
                            continue
                        
                        if email_data:
                            emails.append(email_data)
                except Exception as e:
                    logger.error(f"Error fetching emails from {label_name}: {e}")
            elif not label_id:
                logger.error(f"Label ID for '{label_name}' not found. Cannot fetch emails.")
            else:
                logger.error("Gmail service is not initialized or failed to authenticate. Cannot fetch emails.")
        
        if not emails:
            logger.info("No emails found to process")
            return []
        
        logger.info(f"Found {len(emails)} emails to process")
        processed_orders = []
        
        for email in emails:
            try:
                processed_order = await self._process_single_email(email, review_mode)
                if processed_order:
                    processed_orders.append(processed_order)
            except Exception as e:
                # Ensure we have the email context for error logging
                email_id = getattr(email, 'id', None) or getattr(email, 'gmail_id', 'Unknown')
                email_subject = getattr(email, 'subject', 'Unknown Subject')
                
                logger.error(f"Error processing email {email_id} ('{email_subject}'): {e}")
                
                # Create a failed ProcessedOrder for tracking
                try:
                    failed_order = ProcessedOrder(
                        email_id=email_id,
                        email_subject=email_subject,
                        email_data=email,
                        extracted_data=None,
                        markdown_summary=f"# Processing Failed\n\nError: {str(e)}\n\nEmail could not be processed due to technical issues.",
                        myob_payload=None,
                        markdown_filepath="",
                        myob_filepath=""
                    )
                    
                    # Try to mark email for review
                    try:
                        self.gmail_service.mark_email_failed(email_id)
                        logger.info(f"Marked failed email as 'Failed': {email_id}")
                    except Exception as label_error:
                        logger.error(f"Failed to label email as failed: {label_error}")
                    
                    processed_orders.append(failed_order)
                    
                except Exception as order_error:
                    logger.error(f"Failed to create failed order record: {order_error}")
                
                continue
        
        logger.info(f"Successfully processed {len(processed_orders)} orders")
        return processed_orders

    async def _process_single_email(self, email: EmailData, review_mode: bool = False) -> ProcessedOrder:
        """Process a single email and return a ProcessedOrder."""
        logger.info(f"Processing email: {email.subject}")
        full_content = email.body
        pdf_content = ""
        has_pdf_attachments = False
        
        # Process PDF attachments if they exist
        for attachment in email.attachments:
            if attachment.filename.lower().endswith('.pdf'):
                has_pdf_attachments = True
                try:
                    logger.info(f"Processing PDF: {attachment.filename}")
                    pdf_bytes = attachment.data
                    if pdf_bytes:
                        pdf_text = extract_text_from_pdf(pdf_bytes)
                        if pdf_text:
                            pdf_content += f"\n--- PDF: {attachment.filename} ---\n{pdf_text}\n"
                            full_content += f"\n\n{pdf_text}"
                except Exception as e:
                    logger.error(f"Error processing PDF {attachment.filename}: {e}")
        
        # In review mode, if there's no meaningful content, mark for review instead of failing
        if not full_content.strip():
            if review_mode:
                logger.info(f"Email has no content - skipping labeling: {email.subject}")
                return None
            else:
                raise ValueError("No content found in email or attachments")
        
        # If in review mode and no PDF attachments, create a simple summary
        if review_mode and not has_pdf_attachments:
            logger.info("Creating summary for non-PDF email")
            markdown_summary = self._create_simple_email_summary(email)
            
            logger.info(f"Skipping non-PDF email labeling for: {email.id}")
            
            # Save only markdown summary for review emails
            markdown_filepath = self._save_markdown_file(email.subject, markdown_summary)
            
            return ProcessedOrder(
                email_id=email.id,
                email_subject=email.subject,
                email_data=email,
                extracted_data=None,  # No order data for review emails
                markdown_summary=markdown_summary,
                myob_payload=None,  # No MYOB payload for review emails
                markdown_filepath=markdown_filepath,
                myob_filepath=""
            )
        
        # Standard processing for emails with PDF attachments
        logger.info("Generating markdown summary")
        markdown_summary = await self.llm_service.generate_markdown_summary(
            email_body=email.body,
            subject=email.subject,
            sender=email.sender,  # This should already contain X-Original-Sender if available
            pdf_content=pdf_content
        )
        
        logger.info("Extracting order data with LLM and fuzzy matching")
        try:
            # Pass sender email for fuzzy customer matching
            extracted_data_dict = await self.llm_service.extract_order_from_content(full_content, sender_email=email.sender)
            if not extracted_data_dict:
                logger.info("No order data detected in email - treating as non-order email")
                logger.info(f"Skipping non-order email labeling for: {email.id}")
                
                # Save only markdown summary for non-order emails
                markdown_filepath = self._save_markdown_file(email.subject, markdown_summary)
                
                return ProcessedOrder(
                    email_id=email.id,
                    email_subject=email.subject,
                    email_data=email,
                    extracted_data=None,  # No order data for non-order emails
                    markdown_summary=markdown_summary,
                    myob_payload=None,  # No MYOB payload for non-order emails
                    markdown_filepath=markdown_filepath,
                    myob_filepath=""
                )
        except Exception as e:
            logger.error(f"Order data extraction failed: {e}")
            # For extraction failures, also treat as non-order email
            logger.info("Treating extraction failure as non-order email")
            logger.info(f"Skipping failed extraction email labeling for: {email.id}")
            
            # Save only markdown summary
            markdown_filepath = self._save_markdown_file(email.subject, markdown_summary)
            
            return ProcessedOrder(
                email_id=email.id,
                email_subject=email.subject,
                email_data=email,
                extracted_data=None,  # No order data
                markdown_summary=markdown_summary,
                myob_payload=None,  # No MYOB payload
                markdown_filepath=markdown_filepath,
                myob_filepath=""
            )
        
        # Only try to validate if we have extracted data
        try:
            extracted_order = ExtractedOrder(**extracted_data_dict)
        except ValidationError as e:
            logger.error(f"Order data validation failed: {e}")
            # For validation failures, also treat as non-order email
            logger.info("Treating validation failure as non-order email")
            logger.info(f"Skipping validation failed email labeling for: {email.id}")
            
            # Save only markdown summary
            markdown_filepath = self._save_markdown_file(email.subject, markdown_summary)
            
            return ProcessedOrder(
                email_id=email.id,
                email_subject=email.subject,
                email_data=email,
                extracted_data=None,  # No order data
                markdown_summary=markdown_summary,
                myob_payload=None,  # No MYOB payload
                markdown_filepath=markdown_filepath,
                myob_filepath=""
            )
        except Exception as e:
            logger.error(f"Unexpected error during order validation: {e}")
            # For unexpected errors, also treat as non-order email
            logger.info("Treating unexpected validation error as non-order email")
            try:
                self.gmail_service.mark_email_review(email.id)
                logger.info(f"Marked unexpected error email as 'Review': {email.id}")
            except Exception as label_error:
                logger.error(f"Failed to label email for review: {label_error}")
            
            # Save only markdown summary
            markdown_filepath = self._save_markdown_file(email.subject, markdown_summary)
            
            return ProcessedOrder(
                email_id=email.id,
                email_subject=email.subject,
                email_data=email,
                extracted_data=None,  # No order data
                markdown_summary=markdown_summary,
                myob_payload=None,  # No MYOB payload
                markdown_filepath=markdown_filepath,
                myob_filepath=""
            )
        
        logger.info("Generating MYOB payload")
        try:
            # Convert ExtractedOrder object to dict for MYOB payload generation
            if hasattr(extracted_order, 'model_dump'):
                order_dict = extracted_order.model_dump()
            elif hasattr(extracted_order, 'dict'):
                order_dict = extracted_order.dict()  # Fallback for older Pydantic versions
            else:
                order_dict = extracted_order
            myob_payload = self.llm_service.generate_myob_payload(order_dict)
            if not myob_payload:
                logger.error("Failed to generate MYOB payload")
                raise ValueError("Failed to generate MYOB payload")
        except Exception as e:
            logger.error(f"MYOB payload generation failed: {e}")
            raise ValueError(f"Failed to generate MYOB payload: {e}")
        
        markdown_filepath = self._save_markdown_file(email.subject, markdown_summary)
        myob_filepath = self._save_myob_file(email.subject, myob_payload)
        order_id = self._generate_order_id()
        
        # Create processed order object
        processed_order = ProcessedOrder(
            email_id=email.gmail_id,  # Use gmail_id for consistency
            email_subject=email.subject,
            email_data=email,
            extracted_data=extracted_order,
            markdown_summary=markdown_summary,
            myob_payload=myob_payload,
            markdown_filepath=markdown_filepath,
            myob_filepath=myob_filepath
        )
        
        # Save to Supabase database
        try:
            self._save_to_supabase(processed_order)
        except Exception as db_error:
            logger.error(f"Failed to save to Supabase: {db_error}")
        
        # Mark email as successfully processed in Gmail
        try:
            self.gmail_service.mark_email_processed(email.gmail_id)
            logger.info(f"Marked email as 'Processed': {email.gmail_id}")
        except Exception as label_error:
            logger.error(f"Failed to add 'Processed' label to email {email.gmail_id}: {label_error}")
        
        return processed_order

    async def run_intelligent_workflow(self, label_names: Optional[List[str]] = None, 
                                     max_emails: int = 40, time_filter: Optional[str] = None) -> Dict[str, Any]:
        """Run intelligent email processing with categorization and smart routing."""
        logger.info("🧠 Starting INTELLIGENT email processing workflow")
        
        try:
            # Fetch emails (same as before)
            emails = await self._fetch_emails_for_processing(label_names, max_emails, time_filter)
            if not emails:
                logger.info("No emails found to process")
                return {"total_emails": 0, "classifications": [], "dashboard_report": {}}
            
            logger.info(f"📧 Found {len(emails)} emails to categorize and process")
            
            # Step 1: Categorize ALL emails first
            classifications = []
            processed_orders = []
            
            for email in emails:
                try:
                    # Always mark email as read first to prevent reprocessing
                    self.gmail_service.mark_email_processed(email.gmail_id)
                    logger.info(f"✅ Marked email as read: {email.gmail_id}")
                    
                    # Categorize the email
                    logger.info(f"🔍 Categorizing: {email.subject}")
                    classification = await self.email_categorizer.categorize_email(email)
                    classifications.append(classification)
                    
                    # Route based on classification
                    if classification.is_order:
                        logger.info(f"📦 Processing ORDER: {email.subject}")
                        processed_order = await self._process_order_email(email, classification)
                        if processed_order:
                            processed_orders.append(processed_order)
                    else:
                        logger.info(f"📄 Processing NON-ORDER: {email.subject} (Category: {classification.category.value})")
                        processed_order = await self._process_non_order_email(email, classification)
                        if processed_order:
                            processed_orders.append(processed_order)
                    
                except Exception as e:
                    logger.error(f"❌ Error processing email {email.gmail_id}: {e}")
                    # Create failed classification
                    failed_classification = EmailClassification(
                        category=EmailCategory.OTHER,
                        priority=EmailPriority.MEDIUM,
                        confidence=0.0,
                        is_order=False,
                        summary=f"Processing failed: {str(e)}",
                        next_steps=["Manual review required"],
                        key_entities={},
                        requires_response=True,
                        estimated_response_time="Manual review"
                    )
                    classifications.append(failed_classification)
                    continue
            
            # Generate dashboard report
            dashboard_report = self.email_categorizer.generate_dashboard_report(classifications)
            actionable_insights = self.email_categorizer.get_actionable_insights(classifications)
            
            return {
                "total_emails": len(emails),
                "classifications": classifications,
                "processed_orders": processed_orders,
                "dashboard_report": dashboard_report,
                "actionable_insights": actionable_insights
            }
            
        except Exception as e:
            logger.error(f"❌ Intelligent workflow error: {e}")
            return {"error": str(e), "total_emails": 0}

    async def _fetch_emails_for_processing(self, label_names: Optional[List[str]], max_emails: int, time_filter: Optional[str]) -> List[EmailData]:
        """Fetch emails for processing (extracted from existing logic)."""
        emails = []
        
        # Use provided labels or default ones
        if label_names is None:
            label_names = ['Brady', 'RSEA', 'Woolworths','Brierley', 'Gateway', 'Highgate', 'Sitecraft']
        
        logger.info(f"📥 Fetching emails from labels: {', '.join(label_names)}")
        
        query = time_filter or ""
        if query:
            logger.info(f"🔍 Using query filter: {query}")
        
        for label_name in label_names:
            label_id = self.gmail_service.get_label_id(label_name)
            gmail_service_instance = getattr(self.gmail_service, "service", None)
            
            if label_id and gmail_service_instance is not None:
                try:
                    list_params = {
                        'userId': 'me',
                        'labelIds': [label_id],
                        'maxResults': max_emails
                    }
                    if query:
                        list_params['q'] = query
                    
                    response = gmail_service_instance.users().messages().list(**list_params).execute()
                    messages = response.get('messages', [])
                    logger.info(f"📧 Found {len(messages)} emails in label '{label_name}'")
                    
                    for msg_ref in messages:
                        email_data = self.gmail_service._get_email_details(msg_ref['id'], label_name)
                        
                        # Skip Brady packing slips (existing logic)
                        if (label_name.lower() == 'brady' and email_data and 
                            'packing slip' in email_data.subject.lower()):
                            logger.info(f"⏭️ Skipping Brady packing slip: {email_data.subject}")
                            continue
                        
                        if email_data:
                            emails.append(email_data)
                            
                except Exception as e:
                    logger.error(f"❌ Error fetching emails from {label_name}: {e}")
        
        return emails

    async def _process_order_email(self, email: EmailData, classification: EmailClassification) -> Optional[ProcessedOrder]:
        """Process an email that has been classified as an order."""
        try:
            # Use existing order processing logic
            return await self._process_single_email(email, review_mode=False)
        except Exception as e:
            logger.error(f"❌ Order processing failed for {email.gmail_id}: {e}")
            return None

    async def _process_non_order_email(self, email: EmailData, classification: EmailClassification) -> Optional[ProcessedOrder]:
        """Process an email that is NOT an order - create summary with actionable insights."""
        try:
            # Generate intelligent summary with next steps
            summary_content = f"""# 📧 Email Analysis - {email.subject}

## 📊 Classification Results
- **Category**: {classification.category.value.title()}
- **Priority**: {classification.priority.value.title()}
- **Confidence**: {classification.confidence:.1%}
- **Requires Response**: {'Yes' if classification.requires_response else 'No'}
- **Estimated Response Time**: {classification.estimated_response_time}

## 📝 Summary
{classification.summary}

## 🎯 Next Steps
"""
            for i, step in enumerate(classification.next_steps, 1):
                summary_content += f"{i}. {step}\n"

            summary_content += f"""

## 🔍 Key Information Extracted
"""
            for key, value in classification.key_entities.items():
                if value:
                    summary_content += f"- **{key.replace('_', ' ').title()}**: {value}\n"

            summary_content += f"""

## 📧 Original Email Details
- **From**: {email.sender}
- **Subject**: {email.subject}
- **Date**: {email.received_date}
- **Attachments**: {len(email.attachments)} files

## 📄 Email Content
{email.body[:500]}{'...' if len(email.body) > 500 else ''}

---
*Processed by Intelligent Email Categorization System*
*Category: {classification.category.value} | Priority: {classification.priority.value}*
"""

            # Save the summary
            markdown_filepath = self._save_markdown_file(email.subject, summary_content)
            
            return ProcessedOrder(
                email_id=email.gmail_id,
                email_subject=email.subject,
                email_data=email,
                extracted_data=None,  # Non-order emails don't have order data
                markdown_summary=summary_content,
                myob_payload=None,  # Non-order emails don't have MYOB payloads
                markdown_filepath=markdown_filepath,
                myob_filepath=""
            )
            
        except Exception as e:
            logger.error(f"❌ Non-order processing failed for {email.gmail_id}: {e}")
            return None