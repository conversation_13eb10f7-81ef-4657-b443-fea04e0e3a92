#!/usr/bin/env python3
"""
Test script to verify fuzzy matching integration with email processing
"""

import logging
from debtor_lookup_service import DebtorLookupService

def test_fuzzy_matching_integration():
    """Test the fuzzy matching with real email examples"""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    try:
        # Initialize the debtor lookup service
        service = DebtorLookupService(csv_path="docs/active_debtors.csv", threshold=80)
        
        # Test cases based on your customer data
        test_emails = [
            # Test cases from your CSV data
            ("<EMAIL>", "Woolworths Orders", "Should match WOOLWORTHS LIMITED (10981)"),
            ("<EMAIL>", "Brady Purchasing", "Should match BRADY AUSTRALIA PTY LTD (5760)"),
            ("<EMAIL>", "Bunnings Accounts", "Should match BUNNINGS - HEAD OFFICE (3793)"),
            ("<EMAIL>", "Myer Info", "Should match MYER LIMITED (5463)"),
            ("<EMAIL>", "Coles Orders", "Should match COLES (8893)"),
            ("<EMAIL>", "CW Purchasing", "Should match CW MANAGEMENT variants"),
            ("<EMAIL>", "Team Systems Admin", "Should NOT match (internal email)"),
            ("<EMAIL>", "Unknown Company", "Should not match"),
            
            # Test some specific customers from your CSV
            ("<EMAIL>", "Reflex Equipment", "Should match REFLEX EQUIP - WETHERILL PARK (11197)"),
            ("<EMAIL>", "Dandenong Castors", "Should match DANDENONG WHEELS & CASTORS (2996)"),
            ("<EMAIL>", "AG Coombs", "Should match A. G. COOMBS PTY LTD (19287)"),
        ]
        
        print("=== Fuzzy Matching Integration Test ===\n")
        
        # Show service stats
        stats = service.get_stats()
        print("Service Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        print()
        
        # Test each email
        for i, (email, sender, expected) in enumerate(test_emails, 1):
            print(f"Test {i}: {email}")
            print(f"Sender: {sender}")
            print(f"Expected: {expected}")
            
            result = service.find_debtor_by_email(email, sender)
            
            if result:
                print(f"✓ MATCH FOUND:")
                print(f"  Customer: {result['customer_name']}")
                print(f"  Debtor ID: {result['debtor_id']}")
                print(f"  Confidence: {result['confidence_score']}%")
                print(f"  Method: {result['match_method']}")
                print(f"  Matched text: {result['matched_text']}")
                if result['instructions_path']:
                    print(f"  Instructions: {result['instructions_path']}")
                else:
                    print(f"  Instructions: None")
            else:
                print("✗ NO MATCH FOUND")
            
            print("-" * 80)
        
        # Test search functionality
        print("\n=== Search Functionality Test ===")
        search_queries = ["woolworths", "brady", "bunnings", "myer", "coles", "reflex", "dandenong"]
        
        for query in search_queries:
            print(f"\nSearching for: '{query}'")
            results = service.search_customers(query, limit=3)
            
            if results:
                for j, result in enumerate(results, 1):
                    print(f"  {j}. {result['customer_name']} (ID: {result['debtor_id']}, Score: {result['confidence_score']}%)")
            else:
                print("  No matches found")
        
        print("\n=== Test Complete ===")
        print("✓ Fuzzy matching service is working correctly!")
        print("✓ Ready for integration with email processing system")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(test_fuzzy_matching_integration())