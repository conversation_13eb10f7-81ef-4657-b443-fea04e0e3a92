#!/usr/bin/env python3
"""Test the customer lookup functionality."""

from supabase_database_service import SupabaseService

def test_customer_lookup():
    db = SupabaseService()
    
    # Test cases for Woolworths
    test_names = [
        "Woolworths",
        "WOOLWORTHS",
        "Woolworths Group",
        "ALH GROUP",
        "ALH",
        "woolworths",
        "Unknown Customer"
    ]
    
    print("Testing customer lookup functionality:")
    print("=" * 60)
    
    for name in test_names:
        print(f"\nTesting: '{name}'")
        customer = db.find_customer_by_name(name)
        if customer:
            print(f"  ✅ Found: ID {customer['debtor_id']} - {customer['customer_name']}")
        else:
            print(f"  ❌ Not found")

if __name__ == "__main__":
    test_customer_lookup()