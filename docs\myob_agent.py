"""
MYOB Agent Module
Handles validation of parsed purchase orders against the MYOB Exo system
and submits them for processing.
"""

import os
import json
import logging
import base64
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
import requests

# Local imports
from supabase_clean import supabase_manager

# Load environment variables
load_dotenv()

class MyobAgent:
    """
    Agent for interacting with the MYOB Exo accounting system API.

    This agent is responsible for validating customer and stock information,
    checking pricing and availability, and submitting finalized orders.
    """

    def __init__(self):
        """Initialize the MYOB Agent."""
        self.logger = logging.getLogger(__name__)
        self.supabase = supabase_manager
        
        # Load configuration and validate
        self._load_config()
        self._validate_config()

        # Initialize SKU overrides for specific customers
        self.sku_overrides = {
            "woolworths": "TSSU-ORA",
            "endeavour group": "TSSU-ORA"
        }
        self.logger.info(f"SKU overrides loaded for: {list(self.sku_overrides.keys())}")

        # Discover and cache API endpoints
        self.endpoints = self._load_endpoints()
        if not self.endpoints:
            self.logger.info("No cached endpoints found, discovering from API...")
            self.endpoints = self.discover_endpoints()

    def _load_config(self):
        """Load configuration from environment variables."""
        self.base_url = os.getenv("EXO_BASE_URL")
        self.username = os.getenv("EXO_USER")
        self.password = os.getenv("EXO_PASSWORD")
        self.company_db = os.getenv("EXO_COMPANY_DB")
        self.api_token = os.getenv("EXO_API_TOKEN")
        self.endpoints_file = "myob_endpoints.json"

    def _validate_config(self):
        """Ensure all required MYOB configuration variables are set."""
        required = ["base_url", "username", "password", "company_db", "api_token"]
        for attr in required:
            if not getattr(self, attr):
                raise ValueError(f"Missing required MYOB configuration: EXO_{attr.upper()}")

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Centralized method for making authenticated requests to the MYOB API.
        """
        url = f"{self.base_url}{endpoint}"
        auth_string = f"{self.username}:{self.password}"
        auth_header = base64.b64encode(auth_string.encode('utf-8')).decode('utf-8')
        
        headers = {
            "Authorization": f"Basic {auth_header}",
            "X-Exo-Api-Key": self.api_token,
            "X-Exo-Company-Database": self.company_db,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        self.logger.debug(f"Making {method.upper()} request to {url} with params: {kwargs.get('params')}")
        
        try:
            response = requests.request(method, url, headers=headers, timeout=30, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            self.logger.error(f"MYOB API request failed: {e}")
            # Optionally, log the response body for debugging
            if e.response is not None:
                self.logger.error(f"Response body: {e.response.text}")
            raise

    def _load_endpoints(self) -> Optional[Dict[str, str]]:
        """Load cached API endpoints from a local JSON file."""
        if os.path.exists(self.endpoints_file):
            with open(self.endpoints_file, 'r') as f:
                self.logger.info(f"Loading MYOB endpoints from {self.endpoints_file}")
                return json.load(f)
        return None

    def discover_endpoints(self) -> Dict[str, str]:
        """Discover API endpoints from the MYOB API root."""
        try:
            response = self._make_request('get', '/')
            data = response.json()
            endpoints = {item['name']: item['url'] for item in data.get('value', [])}
            
            with open(self.endpoints_file, 'w') as f:
                json.dump(endpoints, f, indent=4)
            
            self.logger.info(f"Discovered and cached {len(endpoints)} endpoints.")
            return endpoints
        except Exception as e:
            self.logger.error(f"Failed to discover MYOB endpoints: {e}")
            return {}

    def get_endpoint(self, name: str) -> Optional[str]:
        """Retrieve a specific endpoint URL by its name."""
        endpoint = self.endpoints.get(name) if self.endpoints else None
        if not endpoint:
            self.logger.error(f"Endpoint '{name}' not found in cached endpoints.")
        return endpoint

    def process_pending_orders(self):
        """
        Fetches pending orders from Supabase and processes them.
        This is the main entry point for the agent's polling loop.
        """
        self.logger.info("Checking for pending orders to validate...")
        pending_orders = self.supabase.get_pending_orders()
        
        if not pending_orders:
            self.logger.info("No pending orders found.")
            return

        self.logger.info(f"Found {len(pending_orders)} pending orders.")
        for order in pending_orders:
            self.validate_and_submit_order(order)

    def validate_and_submit_order(self, order_data: Dict[str, Any]):
        """Validates a single order and submits it if successful."""
        order_id = order_data['id']
        self.logger.info(f"Starting validation for order ID: {order_id}")
        
        validation_log = []
        is_valid = True

        try:
            # 1. Validate Customer
            customer_name = order_data.get('customer_name')
            debtor_info = self._search_debtor(customer_name or "")
            if not debtor_info:
                is_valid = False
                validation_log.append({"step": "customer_validation", "status": "failed", "message": f"Customer '{customer_name}' not found in MYOB."})
            else:
                validation_log.append({"step": "customer_validation", "status": "success", "message": f"Customer '{customer_name}' found (ACCNO: {debtor_info['ACCNO']})."})
                order_data['debtor_accno'] = debtor_info['ACCNO']

            # 2. Apply SKU Overrides
            self._apply_sku_overrides(order_data)

            # 3. Validate Line Items
            if is_valid and order_data.get('items'):
                for item in order_data['items']:
                    sku = item.get('sku')
                    if not sku:
                        is_valid = False
                        validation_log.append({"step": "item_validation", "status": "failed", "message": f"Item '{item.get('description')}' is missing an SKU."})
                        continue

                    stock_info = self._search_stock_item(sku)
                    if not stock_info:
                        is_valid = False
                        validation_log.append({"step": "item_validation", "status": "failed", "message": f"SKU '{sku}' not found in MYOB."})
                    else:
                        validation_log.append({"step": "item_validation", "status": "success", "message": f"SKU '{sku}' found."})
                        # Further checks can be added here (pricing, availability)

            # 4. Final Decision
            if is_valid:
                self.logger.info(f"Order {order_id} passed validation. Submitting to MYOB.")
                submission_result = self.submit_order(order_data)
                self.supabase.update_order_status(order_id, "submitted", validation_log)
            else:
                self.logger.warning(f"Order {order_id} failed validation. Marking as 'validation_failed'.")
                self.supabase.update_order_status(order_id, "validation_failed", validation_log)

        except Exception as e:
            self.logger.error(f"An unexpected error occurred during validation for order {order_id}: {e}", exc_info=True)
            self.supabase.update_order_status(order_id, "error", [{"step": "system_error", "message": str(e)}])

    def _search_debtor(self, customer_name: str) -> Optional[Dict[str, Any]]:
        """Search for a debtor (customer) by name in MYOB."""
        endpoint = self.get_endpoint("collection/Debtor")
        if not endpoint or not customer_name:
            return None
        
        params = {"$filter": f"NAME eq '{customer_name}'", "$top": 1}
        try:
            response = self._make_request('get', endpoint, params=params)
            results = response.json().get('value', [])
            return results[0] if results else None
        except Exception as e:
            self.logger.error(f"Error searching for debtor '{customer_name}': {e}")
            return None

    def _search_stock_item(self, sku: str) -> Optional[Dict[str, Any]]:
        """Search for a stock item by its SKU (STOCKCODE) in MYOB."""
        endpoint = self.get_endpoint("collection/Stock_Item")
        if not endpoint or not sku:
            return None
            
        params = {"$filter": f"STOCKCODE eq '{sku}'", "$top": 1}
        try:
            response = self._make_request('get', endpoint, params=params)
            results = response.json().get('value', [])
            return results[0] if results else None
        except Exception as e:
            self.logger.error(f"Error searching for stock item '{sku}': {e}")
            return None

    def _apply_sku_overrides(self, order_data: Dict[str, Any]):
        """Apply customer-specific SKU overrides based on business rules."""
        customer_name = order_data.get('customer_name', '').lower()
        override_sku = self.sku_overrides.get(customer_name)

        if override_sku and order_data.get('items'):
            self.logger.info(f"Applying SKU override '{override_sku}' for customer '{customer_name}'.")
            for item in order_data['items']:
                # This logic assumes the override applies to a specific product,
                # e.g., "Tassie Salmon". A more robust check might be needed.
                if "salmon" in item.get('description', '').lower():
                    item['original_sku'] = item.get('sku')
                    item['sku'] = override_sku
                    self.logger.info(f"Overrode SKU for item: {item.get('description')}")

    def submit_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Construct and submit a sales order to MYOB Exo."""
        endpoint = self.get_endpoint("collection/Salesorder")
        if not endpoint:
            return {"success": False, "error": "Salesorder endpoint not found."}

        # Construct the order payload in the format expected by MYOB Exo
        payload = {
            "HDR": {
                "ACCNO": order_data.get('debtor_accno'),
                "CUSTOMERORDERNO": order_data.get('order_number', 'N/A'),
                "CONTACT": order_data.get('customer_name'),
                # Add other header fields as required
            },
            "LINES": [
                {
                    "STOCKCODE": item.get('sku'),
                    "DESCRIPTION": item.get('description'),
                    "QUANTITY": item.get('quantity'),
                    "UNITPRICE": item.get('unit_price'),
                } for item in order_data.get('items', [])
            ]
        }

        try:
            response = self._make_request('post', endpoint, json=payload)
            self.logger.info(f"Successfully submitted order to MYOB. Response: {response.status_code}")
            return {"success": True, "response": response.json()}
        except Exception as e:
            self.logger.error(f"Failed to submit order to MYOB: {e}")
            return {"success": False, "error": str(e)}

# Global instance
myob_agent = MyobAgent()
