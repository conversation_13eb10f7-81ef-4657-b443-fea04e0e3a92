#!/usr/bin/env python3
"""
LLM Service Web API
FastAPI-based web service for the LLM service.
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import the LLM service
from llm_service import LLMService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# Global service instance
llm_service: Optional[LLMService] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage the lifespan of the FastAPI app."""
    global llm_service
    
    # Startup
    logger.info("🚀 Starting LLM Service API...")
    try:
        llm_service = LLMService()
        await llm_service.__aenter__()
        logger.info("✅ LLM Service initialized successfully")
        yield
    except Exception as e:
        logger.error(f"❌ Failed to initialize LLM service: {e}")
        raise
    finally:
        # Shutdown
        if llm_service:
            await llm_service.__aexit__(None, None, None)
            logger.info("🧹 LLM Service cleaned up")

# Create FastAPI app
app = FastAPI(
    title="LLM Service API",
    description="AI-powered email processing and order extraction service",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class TextProcessRequest(BaseModel):
    content: str

class EmailProcessRequest(BaseModel):
    email_body: str
    subject: str
    sender: str
    email_id: Optional[str] = None

class SummaryRequest(BaseModel):
    email_body: str
    subject: str
    sender: str

class HealthResponse(BaseModel):
    status: str
    services: Dict[str, bool]
    timestamp: str

class ProcessResponse(BaseModel):
    success: bool
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# API Routes
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "LLM Service API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    if not llm_service:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        health = await llm_service.health_check()
        return HealthResponse(
            status="healthy" if all(health.values()) else "degraded",
            services=health,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/process/text", response_model=ProcessResponse)
async def process_text(request: TextProcessRequest):
    """Process text content for order extraction."""
    if not llm_service:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        result = await llm_service.extract_order_from_content(request.content)
        if result:
            # Generate MYOB payload
            myob_payload = llm_service.generate_myob_payload(result)
            return ProcessResponse(
                success=True,
                result={
                    "order_data": result,
                    "myob_payload": myob_payload
                }
            )
        else:
            return ProcessResponse(
                success=False,
                error="No order data found in content"
            )
    except Exception as e:
        logger.error(f"Error processing text: {e}")
        return ProcessResponse(
            success=False,
            error=str(e)
        )

@app.post("/process/email", response_model=ProcessResponse)
async def process_email(request: EmailProcessRequest):
    """Process email content."""
    if not llm_service:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        email_id = request.email_id or f"api-{datetime.now().timestamp()}"
        result = await llm_service.process_email(
            email_body=request.email_body,
            subject=request.subject,
            sender=request.sender,
            email_id=email_id
        )
        return ProcessResponse(
            success=True,
            result=result
        )
    except Exception as e:
        logger.error(f"Error processing email: {e}")
        return ProcessResponse(
            success=False,
            error=str(e)
        )

@app.post("/generate/summary")
async def generate_summary(request: SummaryRequest):
    """Generate email summary."""
    if not llm_service:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        summary = await llm_service.generate_markdown_summary(
            email_body=request.email_body,
            subject=request.subject,
            sender=request.sender
        )
        return {
            "success": True,
            "summary": summary,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error generating summary: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/stats")
async def get_stats():
    """Get service statistics."""
    return {
        "service": "LLM Service API",
        "uptime": "Available via health endpoint",
        "version": "1.0.0",
        "endpoints": [
            "/health",
            "/process/text",
            "/process/email", 
            "/generate/summary",
            "/stats"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Starting LLM Service API...")
    print("📡 API will be available at: http://localhost:8000")
    print("📚 API docs will be available at: http://localhost:8000/docs")
    
    uvicorn.run(
        "llm_service_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )