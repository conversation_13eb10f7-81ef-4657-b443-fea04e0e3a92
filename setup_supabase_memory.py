"""
Setup script for Supabase Memory Client integration
Run this to install dependencies and verify setup
"""

import subprocess
import sys
import os
from pathlib import Path

def install_dependencies():
    """Install required dependencies."""
    print("Installing required dependencies...")
    
    try:
        # Install from requirements.txt
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install dependencies: {e}")
        return False

def check_env_file():
    """Check if .env file exists and has required variables."""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("✗ .env file not found")
        print("Please create a .env file with your Supabase credentials:")
        print("""
# Example .env file
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-anon-key
SUPABASE_SECRET=your-service-role-key

# Your existing variables...
GEMINI_API_KEY=your-gemini-key
# ... other variables
""")
        return False
    
    # Check for required Supabase variables
    with open(env_file, 'r') as f:
        content = f.read()
    
    required_vars = ["SUPABASE_URL", "SUPABASE_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if f"{var}=" not in content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"✗ Missing required variables in .env: {missing_vars}")
        return False
    
    print("✓ .env file found with required Supabase variables")
    return True

def main():
    """Main setup function."""
    print("=" * 60)
    print("SUPABASE MEMORY CLIENT SETUP")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("config.py").exists():
        print("✗ config.py not found. Please run this from the TeamsysV0.1 directory")
        sys.exit(1)
    
    print("✓ Running from correct directory")
    
    # Check .env file
    if not check_env_file():
        print("\n❌ Environment setup incomplete. Please fix .env file and try again.")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Dependency installation failed.")
        sys.exit(1)
    
    # Run test
    print("\nRunning Supabase memory client test...")
    try:
        subprocess.check_call([sys.executable, "test_supabase_memory.py"])
        print("\n✅ Setup completed successfully!")
        print("Your Supabase Memory Client is ready to use.")
        print("\nNext steps:")
        print("1. Your LLM service now uses Supabase for memory storage")
        print("2. Run your email processing as normal")
        print("3. Memory will be stored in your Supabase database")
        
    except subprocess.CalledProcessError:
        print("\n⚠️  Setup completed but tests failed.")
        print("Please check your Supabase configuration and try running:")
        print("python test_supabase_memory.py")

if __name__ == "__main__":
    main()
