#!/usr/bin/env python3
"""
Customer Sync Service - Syncs active customers from MYOB to Supabase
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from myob_service import MyobService
from supabase_database_service import SupabaseService

logger = logging.getLogger(__name__)

class CustomerSyncService:
    """Service to sync active customers from MYOB to Supabase."""
    
    def __init__(self):
        self.myob_service = MyobService()
        self.supabase_service = SupabaseService()
        logger.info("Initialized Customer Sync Service")
    
    def sync_active_customers(self, months_back: int = 24, dry_run: bool = False) -> Dict[str, Any]:
        """
        Sync active customers from MYOB to Supabase.
        
        Args:
            months_back: Number of months to look back for active transactions
            dry_run: If True, only report what would be done without making changes
            
        Returns:
            Dictionary with sync results and statistics
        """
        logger.info(f"Starting customer sync (last {months_back} months, dry_run={dry_run})")
        
        results = {
            'success': False,
            'active_customers_from_myob': 0,
            'existing_customers_in_supabase': 0,
            'customers_to_add': 0,
            'customers_to_remove': 0,
            'customers_added': 0,
            'customers_removed': 0,
            'errors': []
        }
        
        try:
            # Step 1: Get active customers from MYOB
            logger.info("Fetching active customers from MYOB...")
            active_myob_customers = self.myob_service.get_active_debtors(months_back=months_back)
            
            if active_myob_customers is None:
                results['errors'].append("Failed to fetch active customers from MYOB")
                return results
            
            results['active_customers_from_myob'] = len(active_myob_customers)
            logger.info(f"Found {len(active_myob_customers)} active customers in MYOB")
            
            # Step 2: Get existing customers from Supabase
            logger.info("Fetching existing customers from Supabase...")
            existing_supabase_customers = self.supabase_service.get_all_customers()
            results['existing_customers_in_supabase'] = len(existing_supabase_customers)
            logger.info(f"Found {len(existing_supabase_customers)} existing customers in Supabase")
            
            # Step 3: Determine which customers to add/remove
            active_debtor_ids = {customer.get('id') for customer in active_myob_customers if customer.get('id')}
            existing_debtor_ids = {customer.get('debtor_id') for customer in existing_supabase_customers if customer.get('debtor_id')}
            
            customers_to_add = [
                customer for customer in active_myob_customers 
                if customer.get('id') not in existing_debtor_ids
            ]
            
            customers_to_remove_ids = existing_debtor_ids - active_debtor_ids
            
            results['customers_to_add'] = len(customers_to_add)
            results['customers_to_remove'] = len(customers_to_remove_ids)
            
            logger.info(f"Customers to add: {len(customers_to_add)}")
            logger.info(f"Customers to remove: {len(customers_to_remove_ids)}")
            
            if dry_run:
                logger.info("DRY RUN - No changes will be made")
                self._log_sync_preview(customers_to_add, customers_to_remove_ids, existing_supabase_customers)
                results['success'] = True
                return results
            
            # Step 4: Add new active customers
            if customers_to_add:
                logger.info(f"Adding {len(customers_to_add)} new active customers...")
                added_count = self._add_customers_to_supabase(customers_to_add)
                results['customers_added'] = added_count
                logger.info(f"Successfully added {added_count} customers")
            
            # Step 5: Remove inactive customers
            if customers_to_remove_ids:
                logger.info(f"Removing {len(customers_to_remove_ids)} inactive customers...")
                removed_count = self._remove_customers_from_supabase(customers_to_remove_ids)
                results['customers_removed'] = removed_count
                logger.info(f"Successfully removed {removed_count} customers")
            
            results['success'] = True
            logger.info("Customer sync completed successfully")
            
        except Exception as e:
            error_msg = f"Customer sync failed: {str(e)}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
        
        return results
    
    def _add_customers_to_supabase(self, customers: List[Dict[str, Any]]) -> int:
        """Add customers to Supabase database."""
        added_count = 0
        
        for customer in customers:
            try:
                # Map MYOB customer fields to Supabase schema
                supabase_customer = self._map_myob_to_supabase_customer(customer)
                
                # Insert customer into Supabase
                result = self.supabase_service.supabase.table("customers").insert(supabase_customer).execute()
                
                if result.data:
                    added_count += 1
                    logger.debug(f"Added customer: {customer.get('accountname', 'Unknown')} (ID: {customer.get('id')})")
                else:
                    logger.warning(f"Failed to add customer: {customer.get('accountname', 'Unknown')}")
                    
            except Exception as e:
                logger.error(f"Error adding customer {customer.get('accountname', 'Unknown')}: {e}")
        
        return added_count
    
    def _remove_customers_from_supabase(self, debtor_ids: set) -> int:
        """Remove customers from Supabase database."""
        removed_count = 0
        
        for debtor_id in debtor_ids:
            try:
                # Delete customer from Supabase
                result = self.supabase_service.supabase.table("customers").delete().eq("debtor_id", debtor_id).execute()
                
                if result.data:
                    removed_count += 1
                    logger.debug(f"Removed customer with debtor_id: {debtor_id}")
                else:
                    logger.warning(f"Failed to remove customer with debtor_id: {debtor_id}")
                    
            except Exception as e:
                logger.error(f"Error removing customer {debtor_id}: {e}")
        
        return removed_count
    
    def _map_myob_to_supabase_customer(self, myob_customer: Dict[str, Any]) -> Dict[str, Any]:
        """Map MYOB customer fields to Supabase customer schema."""
        return {
            'debtor_id': myob_customer.get('id'),  # MYOB debtors use 'id' field
            'customer_name': myob_customer.get('accountname', '').strip(),  # MYOB uses 'accountname'
            'email': myob_customer.get('email', '').strip() if myob_customer.get('email') else None,
            'phone': myob_customer.get('phone', '').strip() if myob_customer.get('phone') else None,
            'address_line1': self._extract_address_line(myob_customer.get('postaladdress', ''), 0),
            'address_line2': self._extract_address_line(myob_customer.get('postaladdress', ''), 1),
            'address_line3': self._extract_address_line(myob_customer.get('postaladdress', ''), 2),
            'address_line4': self._extract_address_line(myob_customer.get('postaladdress', ''), 3),
            'contact_person': myob_customer.get('contactname', '').strip() if myob_customer.get('contactname') else None,
            'payment_terms': None,  # Would need to lookup credit terms by ID
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
    
    def _extract_address_line(self, full_address: str, line_index: int) -> Optional[str]:
        """Extract a specific line from a full address string."""
        if not full_address:
            return None
        
        lines = [line.strip() for line in full_address.split('\n') if line.strip()]
        if line_index < len(lines):
            return lines[line_index]
        return None
    
    def _log_sync_preview(self, customers_to_add: List[Dict[str, Any]], 
                         customers_to_remove_ids: set, 
                         existing_customers: List[Dict[str, Any]]):
        """Log a preview of what the sync would do."""
        logger.info("=== SYNC PREVIEW ===")
        
        if customers_to_add:
            logger.info("Customers to ADD:")
            for customer in customers_to_add[:10]:  # Show first 10
                logger.info(f"  + {customer.get('accountname', 'Unknown')} (ID: {customer.get('id')})")
            if len(customers_to_add) > 10:
                logger.info(f"  ... and {len(customers_to_add) - 10} more")
        
        if customers_to_remove_ids:
            logger.info("Customers to REMOVE:")
            existing_by_id = {c['debtor_id']: c for c in existing_customers}
            for debtor_id in list(customers_to_remove_ids)[:10]:  # Show first 10
                customer_name = existing_by_id.get(debtor_id, {}).get('customer_name', 'Unknown')
                logger.info(f"  - {customer_name} (ID: {debtor_id})")
            if len(customers_to_remove_ids) > 10:
                logger.info(f"  ... and {len(customers_to_remove_ids) - 10} more")
        
        logger.info("=== END PREVIEW ===")
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get current sync status and statistics."""
        try:
            # Get counts from both systems
            myob_active = self.myob_service.get_active_debtors(months_back=24)
            supabase_customers = self.supabase_service.get_all_customers()
            
            myob_count = len(myob_active) if myob_active else 0
            supabase_count = len(supabase_customers) if supabase_customers else 0
            
            return {
                'myob_active_customers': myob_count,
                'supabase_customers': supabase_count,
                'last_check': datetime.now().isoformat(),
                'sync_needed': myob_count != supabase_count
            }
            
        except Exception as e:
            logger.error(f"Error getting sync status: {e}")
            return {
                'error': str(e),
                'last_check': datetime.now().isoformat()
            }


def main():
    """CLI interface for customer sync."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Sync active customers from MYOB to Supabase')
    parser.add_argument('--months', type=int, default=24, help='Months to look back for active customers (default: 24)')
    parser.add_argument('--dry-run', action='store_true', help='Preview changes without making them')
    parser.add_argument('--status', action='store_true', help='Show current sync status')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    sync_service = CustomerSyncService()
    
    if args.status:
        print("Getting sync status...")
        status = sync_service.get_sync_status()
        print(f"MYOB Active Customers: {status.get('myob_active_customers', 'Error')}")
        print(f"Supabase Customers: {status.get('supabase_customers', 'Error')}")
        print(f"Sync Needed: {status.get('sync_needed', 'Unknown')}")
        print(f"Last Check: {status.get('last_check', 'Unknown')}")
        if 'error' in status:
            print(f"Error: {status['error']}")
    else:
        print(f"Starting customer sync (months_back={args.months}, dry_run={args.dry_run})")
        results = sync_service.sync_active_customers(months_back=args.months, dry_run=args.dry_run)
        
        print("\n=== SYNC RESULTS ===")
        print(f"Success: {results['success']}")
        print(f"Active customers from MYOB: {results['active_customers_from_myob']}")
        print(f"Existing customers in Supabase: {results['existing_customers_in_supabase']}")
        print(f"Customers to add: {results['customers_to_add']}")
        print(f"Customers to remove: {results['customers_to_remove']}")
        print(f"Customers added: {results['customers_added']}")
        print(f"Customers removed: {results['customers_removed']}")
        
        if results['errors']:
            print("\nErrors:")
            for error in results['errors']:
                print(f"  - {error}")


if __name__ == "__main__":
    main()