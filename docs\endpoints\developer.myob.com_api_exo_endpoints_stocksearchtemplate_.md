---
url: "https://developer.myob.com/api/exo/endpoints/stocksearchtemplate/"
title: "Stock Search Template"
---

[![](https://developer.myob.com/media/cms_page_media/23/myob_logo_small.png)](http://www.myob.com/)

# Stock Search Template

List all stock search templates or return search results based on a stock search template set up in MYOB EXO Business.

**Date Released:** May 30th 2014 **Date Updated:** May 30th 2014

| URL | Supports |
| --- | --- |
| {URI}/stocksearchtemplate<br>{URI}/stocksearchtemplate/{id}{parameters} | [GET](https://developer.myob.com/api/exo/endpoints/stocksearchtemplate/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/stocksearchtemplate/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/stocksearchtemplate/#POST)<br>[GET](https://developer.myob.com/api/exo/endpoints/stocksearchtemplate/#GET) \| <br>[PUT](https://developer.myob.com/api/exo/endpoints/stocksearchtemplate/#PUT) \| <br>[POST](https://developer.myob.com/api/exo/endpoints/stocksearchtemplate/#POST) |
| {URI} is exo.api.myob.com when connecting to the cloud or the address of the machine hosting the API when connecting on premise. |

Calling the endpoint by itself returns a list of stock search templates (search templates whose `type` is "S").

To use a stock search template to return search results, call the endpoint with the ID number of the search template you want to use, for example:

`http://exo.api.myob.com/stocksearchtemplate/1`

Depending on the template being called, the call may need to pass parameters to filter the search results, for example:

`http://exo.api.myob.com/stocksearchtemplate/1?accno=21&q=airfil`

This will return search results using the search template with the ID number 1 and passing in the Debtor account number 21 and the search string “airfil” as parameters.

The elements list below details information for Stock Search Template. To view the descriptions for the elements you can either hover any attribute to reveal details [or click here to show all details inline.](https://developer.myob.com/api/exo/endpoints/stocksearchtemplate/#reveal)

#### Attribute Details

- name string,null
- Type: string,null
- active boolean
- Type: boolean
- type string,null
- Type: string,null
- parameters array,null
- Type: array,null
- rel string,null
- Type: string,null
- title string,null
- Type: string,null
- id integer
- Required on PUT
- href string,null
- Type: string,null

#### Example json GET response

- {
  - 0
    - {
      - name : Available Stock
      - active : true
      - type : S
      - parameters
        - {
          - 0
            - {
              - paramname : LOCNO
              - paramtype : INTEGER
            - }
        - }
      - id : 2
      - href : {URI}/searchtemplate/2
    - }
  - 1
    - {
      - name : Last 100 Items Sold To This Account
      - active : true
      - type : S
      - parameters
        - {
          - 0
            - {
              - paramname : ACCNO
              - paramtype : INTEGER
            - }
        - }
      - id : 1
      - href : {URI}/searchtemplate/1
    - }
  - 2
    - {
      - name : Previous Invoice to this Account
      - active : true
      - type : S
      - parameters
        - {
          - 0
            - {
              - paramname : ACCNO
              - paramtype : INTEGER
            - }
        - }
      - id : 3
      - href : {URI}/searchtemplate/3
    - }
  - 3
    - {
      - name : Sell Down Items
      - active : true
      - type : S
      - parameters
        - {
          - 0
            - {
              - paramname : ACCNO
              - paramtype : INTEGER
            - }
        - }
      - id : 5
      - href : {URI}/searchtemplate/5
    - }
  - 4
    - {
      - name : Top 10 Items Sold To This Account By Value
      - active : true
      - type : S
      - parameters
        - {
          - 0
            - {
              - paramname : ACCNO
              - paramtype : INTEGER
            - }
        - }
      - id : 4
      - href : {URI}/searchtemplate/4
    - }
- }

{URI} is defined as: http://exo.api.myob.com/

|     |     |
| --- | --- |
|  |  |