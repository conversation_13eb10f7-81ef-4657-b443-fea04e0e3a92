"""Tests for payload generator."""

import pytest
from llm_service.processors.payload_generator import PayloadGenerator
from llm_service.models.schemas import ExtractedOrder, CustomerDetails, OrderLine


class TestPayloadGenerator:
    """Tests for PayloadGenerator."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.generator = PayloadGenerator()
    
    def test_generate_myob_payload_direct(self):
        """Test direct payload generation."""
        order = ExtractedOrder(
            customer_details=CustomerDetails(
                debtor_id=12345,
                customer_order_number="PO-12345"
            ),
            order_lines=[
                OrderLine(stockcode="ABC123", orderquantity=2.0),
                OrderLine(stockcode="DEF456", orderquantity=1.0)
            ],
            X_SHIPVIA="Standard"
        )
        
        payload = self.generator.generate_myob_payload_direct(order)
        
        assert payload["debtorid"] == 12345
        assert payload["customerordernumber"] == "PO-12345"
        assert len(payload["lines"]) == 2
        assert payload["extrafields"][0]["key"] == "X_SHIPVIA"
    
    def test_validate_myob_payload(self):
        """Test payload validation."""
        valid_payload = {
            "debtorid": 12345,
            "lines": [{"stockcode": "ABC123", "orderquantity": 1.0}]
        }
        
        assert self.generator.validate_myob_payload(valid_payload) is True
    
    def test_validate_invalid_payload(self):
        """Test invalid payload validation."""
        invalid_payload = {
            "debtorid": 12345,
            "lines": []  # Empty lines
        }
        
        with pytest.raises(Exception):
            self.generator.validate_myob_payload(invalid_payload)