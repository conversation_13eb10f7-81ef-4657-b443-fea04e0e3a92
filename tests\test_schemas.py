"""Tests for schema models."""

import pytest
from llm_service.models.schemas import (
    CustomerDetails, OrderLine, ExtractedOrder, 
    EmailSummary, MYOBPayload
)


class TestCustomerDetails:
    """Tests for CustomerDetails model."""
    
    def test_valid_customer_details(self):
        """Test valid customer details."""
        details = CustomerDetails(debtor_id=12345, customer_order_number=None)
        assert details.debtor_id == 12345
        assert details.customer_order_number is None
    
    def test_invalid_debtor_id(self):
        """Test invalid debtor ID."""
        with pytest.raises(ValueError):
            CustomerDetails(debtor_id=-1, customer_order_number=None)
    
    def test_with_customer_order_number(self):
        """Test with customer order number."""
        details = CustomerDetails(
            debtor_id=12345,
            customer_order_number="PO-12345"
        )
        assert details.customer_order_number == "PO-12345"


class TestOrderLine:
    """Tests for OrderLine model."""
    
    def test_valid_order_line(self):
        """Test valid order line."""
        line = OrderLine(stockcode="ABC123", orderquantity=5.0)
        assert line.stockcode == "ABC123"
        assert line.orderquantity == 5.0
    
    def test_invalid_stockcode(self):
        """Test invalid stockcode."""
        with pytest.raises(ValueError):
            OrderLine(stockcode="", orderquantity=1.0)
    
    def test_invalid_quantity(self):
        """Test invalid quantity."""
        with pytest.raises(ValueError):
            OrderLine(stockcode="ABC123", orderquantity=-1.0)


class TestExtractedOrder:
    """Tests for ExtractedOrder model."""
    
    def test_valid_extracted_order(self):
        """Test valid extracted order."""
        order = ExtractedOrder(
            customer_details=CustomerDetails(debtor_id=12345, customer_order_number=None),
            order_lines=[OrderLine(stockcode="ABC123", orderquantity=1.0)]
        )
        assert order.customer_details.debtor_id == 12345
        assert len(order.order_lines) == 1
    
    def test_empty_order_lines(self):
        """Test empty order lines."""
        with pytest.raises(ValueError):
            ExtractedOrder(
                customer_details=CustomerDetails(debtor_id=12345, customer_order_number=None),
                order_lines=[]
            )


class TestEmailSummary:
    """Tests for EmailSummary model."""
    
    def test_valid_email_summary(self):
        """Test valid email summary."""
        summary = EmailSummary(
            summary="Test summary",
            action_required="Test action"
        )
        assert summary.summary == "Test summary"
        assert summary.action_required == "Test action"


class TestMYOBPayload:
    """Tests for MYOBPayload model."""
    
    def test_valid_myob_payload(self):
        """Test valid MYOB payload."""
        payload = MYOBPayload(
            debtorid=12345,
            lines=[{"stockcode": "ABC123", "orderquantity": 1.0}]
        )
        assert payload.debtorid == 12345
        assert len(payload.lines) == 1
    
    def test_empty_lines(self):
        """Test empty lines."""
        with pytest.raises(ValueError):
            MYOBPayload(debtorid=12345, lines=[])