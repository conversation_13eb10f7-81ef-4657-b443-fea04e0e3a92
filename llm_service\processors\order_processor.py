"""Order processor for extracting order data."""

import json
import logging
from typing import Dict, Any, Optional, List

from ..models.schemas import ExtractedOrder, CustomerDetails, OrderLine
from ..services.mistral_service import MistralService
from ..services.memory_service import MemoryService
from ..core.exceptions import ValidationException
from ..prompts.prompt_manager import prompt_manager

# Import the fuzzy matching service
try:
    import sys
    import os
    # Add the root directory to the Python path to import debtor_lookup_service
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    if root_dir not in sys.path:
        sys.path.insert(0, root_dir)
    
    from debtor_lookup_service import DebtorLookupService
    FUZZY_MATCHING_AVAILABLE = True
    logging.info("Fuzzy matching service imported successfully")
except ImportError as e:
    FUZZY_MATCHING_AVAILABLE = False
    logging.warning(f"Fuzzy matching service not available: {e} - falling back to Supabase lookup only")

logger = logging.getLogger(__name__)


class OrderProcessor:
    """Processor for extracting order data from content."""
    
    def __init__(self, mistral_service: MistralService, memory_service: MemoryService, supabase_service=None):
        self.mistral_service = mistral_service
        self.memory_service = memory_service
        self.supabase_service = supabase_service
        
        # Initialize fuzzy matching service if available
        self.fuzzy_lookup = None
        if FUZZY_MATCHING_AVAILABLE:
            try:
                self.fuzzy_lookup = DebtorLookupService(csv_path="docs/active_debtors.csv", threshold=80)
                logger.info("Fuzzy matching service initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize fuzzy matching service: {e}")
                self.fuzzy_lookup = None
    
    async def extract_order_data(self, content: str, sender_email: str = None) -> Optional[Dict[str, Any]]:
        """Extract structured order data from content using enhanced LLM with memory context and fuzzy matching."""
        
        # Get relevant context from memory database
        memory_contexts = self.memory_service.get_relevant_context(content[:500], n_results=3)
        context_text = ""
        if memory_contexts:
            context_text = "\nRelevant previous examples:\n" + "\n---\n".join(memory_contexts)
        
        # Use centralized prompt manager for order extraction
        prompt = prompt_manager.get_order_extraction_prompt(content, context_text)
        
        try:
            # Use the enhanced Mistral service with JSON mode
            response = await self.mistral_service.generate_content(prompt)
            
            if response.text:
                try:
                    # The enhanced service should return clean JSON
                    data = json.loads(response.text)
                    
                    # Validate the structure
                    if self._validate_extracted_structure(data):
                        logger.info("Successfully extracted order data with enhanced LLM")
                        return data
                    else:
                        logger.warning("Extracted data does not match expected structure")
                        return None
                        
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response even after cleaning: {e}")
                    logger.error(f"Response content: {response.text[:500]}...")
                    return None
            
            logger.warning("LLM did not return expected JSON response")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting order data with LLM: {str(e)}")
            return None
    
    def _validate_extracted_structure(self, data: Dict[str, Any]) -> bool:
        """Validate that extracted data has the expected structure."""
        try:
            # Check required top-level keys
            if not isinstance(data, dict):
                return False
            
            required_keys = ['customer_details', 'order_lines', 'order_status']
            if not all(key in data for key in required_keys):
                logger.warning(f"Missing required keys. Expected: {required_keys}, Got: {list(data.keys())}")
                return False
            
            # Check customer_details structure
            customer_details = data.get('customer_details', {})
            if not isinstance(customer_details, dict):
                return False
            
            # Check order_lines structure
            order_lines = data.get('order_lines', [])
            if not isinstance(order_lines, list):
                return False
            
            # Validate each order line
            for line in order_lines:
                if not isinstance(line, dict):
                    return False
                if 'stockcode' not in line or 'orderquantity' not in line:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating extracted structure: {e}")
            return False
    
    def _lookup_customer_by_email(self, sender_email: str, sender_name: str = None) -> Optional[Dict[str, Any]]:
        """Lookup customer using fuzzy matching on email address."""
        if not sender_email or not self.fuzzy_lookup:
            return None
        
        try:
            match = self.fuzzy_lookup.find_debtor_by_email(sender_email, sender_name)
            if match:
                logger.info(f"Fuzzy match found: {match['customer_name']} (ID: {match['debtor_id']}, Confidence: {match['confidence_score']}%)")
                return match
            return None
        except Exception as e:
            logger.error(f"Error in fuzzy email lookup for '{sender_email}': {e}")
            return None
    
    def _lookup_customer_by_name(self, customer_name: str) -> Optional[int]:
        """Lookup customer debtor_id by name using enhanced fuzzy matching first, then Supabase fallback."""
        if not customer_name:
            return None
        
        # Try enhanced fuzzy matching first if available
        if self.fuzzy_lookup:
            try:
                # Use the new direct customer name matching method
                match = self.fuzzy_lookup.find_debtor_by_customer_name(customer_name)
                if match:
                    logger.info(f"Direct customer name match: {match['customer_name']} (ID: {match['debtor_id']}, Score: {match['confidence_score']}%)")
                    return match['debtor_id']
                
                # Fallback to search method for broader matching
                results = self.fuzzy_lookup.search_customers(customer_name, limit=1)
                if results and results[0]['confidence_score'] >= 70:  # Lower threshold for search
                    match = results[0]
                    logger.info(f"Search-based name match: {match['customer_name']} (ID: {match['debtor_id']}, Score: {match['confidence_score']}%)")
                    return match['debtor_id']
            except Exception as e:
                logger.warning(f"Fuzzy name lookup failed for '{customer_name}': {e}")
        
        # Fallback to Supabase lookup
        if self.supabase_service:
            try:
                customer = self.supabase_service.find_customer_by_name(customer_name)
                if customer:
                    logger.info(f"Supabase lookup found: {customer_name} -> {customer.get('debtor_id')}")
                    return customer.get('debtor_id')
            except Exception as e:
                logger.error(f"Error in Supabase lookup for '{customer_name}': {e}")
        
        return None
    
    async def validate_extracted_order(self, data: Dict[str, Any], sender_email: str = None, sender_name: str = None) -> bool:
        """Validate extracted order data with improved debtor_id handling and fuzzy matching."""
        try:
            # Ensure customer_details is a dict
            if 'customer_details' not in data or not isinstance(data['customer_details'], dict):
                data['customer_details'] = {
                    'debtor_id': None,  # Use None instead of 0 for unknown customers
                    'customer_order_number': '',
                    'customer_name': 'UNKNOWN CUSTOMER',
                    'delivery_address': '',
                    'shipping_method': 'BEST WAY'
                }
            
            # Handle debtor_id properly - convert to int or set to None
            debtor_id = data['customer_details'].get('debtor_id')
            if debtor_id is not None:
                if isinstance(debtor_id, str):
                    try:
                        # Try to convert string to int
                        parsed_id = int(debtor_id.strip())
                        # If it's 0, treat as unknown customer (set to None)
                        data['customer_details']['debtor_id'] = parsed_id if parsed_id > 0 else None
                    except ValueError:
                        logger.warning(f"Invalid debtor_id format: {debtor_id}, setting to None")
                        data['customer_details']['debtor_id'] = None
                elif isinstance(debtor_id, (int, float)):
                    # If it's 0 or negative, treat as unknown customer
                    data['customer_details']['debtor_id'] = int(debtor_id) if debtor_id > 0 else None
                else:
                    data['customer_details']['debtor_id'] = None
            
            # If no debtor_id found, try fuzzy matching by customer name first, then by email
            final_debtor_id = data['customer_details']['debtor_id']
            if final_debtor_id is None:
                # Priority 1: Try fuzzy matching by customer name (most accurate for direct matches)
                customer_name = data['customer_details'].get('customer_name')
                if customer_name and customer_name.strip().upper() not in ['UNKNOWN CUSTOMER', 'UNKNOWN', '']:
                    logger.info(f"Attempting customer name lookup for: '{customer_name}'")
                    looked_up_debtor_id = self._lookup_customer_by_name(customer_name)
                    if looked_up_debtor_id:
                        data['customer_details']['debtor_id'] = looked_up_debtor_id
                        final_debtor_id = looked_up_debtor_id
                        logger.info(f"✅ Customer name match: '{customer_name}' -> debtor_id: {looked_up_debtor_id}")
                        
                        # Store fuzzy match metadata for reference
                        data['customer_details']['_fuzzy_match'] = {
                            'method': 'customer_name_direct',
                            'confidence': 95,  # High confidence for direct name matches
                            'matched_text': f"Direct name match: {customer_name}"
                        }
                    else:
                        logger.warning(f"❌ Customer name lookup failed for: '{customer_name}'")
                
                # Priority 2: If customer name lookup failed, try by email address
                if final_debtor_id is None and sender_email:
                    logger.info(f"Attempting fuzzy email lookup for: '{sender_email}'")
                    email_match = self._lookup_customer_by_email(sender_email, sender_name)
                    if email_match:
                        data['customer_details']['debtor_id'] = email_match['debtor_id']
                        data['customer_details']['customer_name'] = email_match['customer_name']
                        final_debtor_id = email_match['debtor_id']
                        logger.info(f"✅ Fuzzy email match: {email_match['customer_name']} (ID: {final_debtor_id}, Confidence: {email_match['confidence_score']}%)")
                        
                        # Store fuzzy match metadata for reference
                        data['customer_details']['_fuzzy_match'] = {
                            'method': email_match['match_method'],
                            'confidence': email_match['confidence_score'],
                            'matched_text': email_match['matched_text']
                        }
                
                # Final check
                if final_debtor_id is None:
                    logger.warning("❌ No valid debtor_id found - order will require manual review")
                    logger.info(f"📧 Sender email: {sender_email or 'Not provided'}")
                    logger.info(f"👤 Sender name: {sender_name or 'Not provided'}")
                    logger.info(f"🏢 Extracted customer name: {data['customer_details'].get('customer_name', 'Not extracted')}")
            else:
                logger.info(f"Extracted debtor_id: {final_debtor_id}")
            
            # Ensure order_lines is a list
            if 'order_lines' not in data or not isinstance(data['order_lines'], list):
                data['order_lines'] = []
            
            # Ensure all order lines have required fields
            validated_lines = []
            for line in data['order_lines']:
                if isinstance(line, dict) and 'stockcode' in line and 'orderquantity' in line:
                    try:
                        validated_line = {
                            'stockcode': str(line['stockcode']).strip().replace(' ', ''),  # Remove all whitespace
                            'orderquantity': float(line['orderquantity'])
                        }
                        if validated_line['orderquantity'] > 0:  # Only include positive quantities
                            validated_lines.append(validated_line)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Skipping invalid order line: {line}, error: {e}")
                        continue
            
            data['order_lines'] = validated_lines
            
            # Validate with Pydantic - but handle debtor_id as Optional[int]
            # We need to update the model to handle None values properly
            if data['customer_details']['debtor_id'] is None:
                # For Pydantic validation, we'll use 0 but flag it for review
                data['customer_details']['debtor_id'] = 0
                logger.info("Setting debtor_id to 0 for Pydantic validation - order flagged for review")
            
            order = ExtractedOrder(**data)
            return True
            
        except Exception as e:
            logger.error(f"Order validation failed: {str(e)}")
            raise ValidationException(f"Invalid order data: {str(e)}")