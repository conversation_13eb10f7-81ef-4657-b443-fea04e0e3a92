#!/usr/bin/env python3
"""
Intelligent Email Processor Runner
Run the new intelligent email categorization and processing system.
"""

import asyncio
import logging
import argparse
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def run_intelligent_processing():
    """Run the intelligent email processing system."""
    
    print("🧠 INTELLIGENT EMAIL PROCESSING SYSTEM")
    print("=" * 60)
    print("This system will:")
    print("✅ Categorize ALL emails (orders and non-orders)")
    print("✅ Mark ALL emails as READ (prevent reprocessing)")
    print("✅ Process orders through existing workflow")
    print("✅ Provide actionable next steps for non-orders")
    print("✅ Generate dashboard reports")
    print("=" * 60)
    
    try:
        # Import here to avoid circular imports
        from main_processor import EmailOrderProcessor
        
        # Initialize the processor
        processor = EmailOrderProcessor()
        
        # Run the intelligent workflow
        print("\n🚀 Starting intelligent email processing...")
        results = await processor.run_intelligent_workflow()
        
        if results.get('error'):
            print(f"❌ Error: {results['error']}")
            return
        
        # Display final summary
        print(f"\n🎉 PROCESSING COMPLETE!")
        print(f"📧 Total emails processed: {results['total_emails']}")
        print(f"📦 Orders found: {results['dashboard_report']['orders_found']}")
        print(f"📝 Non-orders processed: {results['total_emails'] - results['dashboard_report']['orders_found']}")
        print(f"🚨 High priority items: {results['dashboard_report']['high_priority']}")
        print(f"📋 Responses required: {results['dashboard_report']['response_required']}")
        
        # Show actionable insights
        insights = results.get('actionable_insights', [])
        if insights:
            print(f"\n🎯 KEY ACTIONABLE INSIGHTS:")
            for insight in insights:
                print(f"  🔸 {insight['title']}: {insight['action']}")
        
        print(f"\n📁 Files saved:")
        print(f"  📄 Summaries: markdown/ folder")
        print(f"  💼 Order payloads: myob/ folder")
        
        print(f"\n✅ All emails marked as READ - no reprocessing will occur")
        
    except Exception as e:
        logger.error(f"❌ Error in intelligent processing: {e}")
        print(f"❌ Error: {e}")

async def run_with_options(max_emails: int = 20, labels: list = None, time_filter: str = None):
    """Run with specific options."""
    
    try:
        from main_processor import EmailOrderProcessor
        
        processor = EmailOrderProcessor()
        
        print(f"\n🔧 Running with options:")
        print(f"  📧 Max emails: {max_emails}")
        print(f"  🏷️ Labels: {labels or 'Default labels'}")
        print(f"  ⏰ Time filter: {time_filter or 'No filter'}")
        
        results = await processor.run_intelligent_workflow(
            label_names=labels,
            max_emails=max_emails,
            time_filter=time_filter
        )
        
        if results.get('error'):
            print(f"❌ Error: {results['error']}")
            return
        
        print(f"\n✅ Processed {results['total_emails']} emails successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        print(f"❌ Error: {e}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Intelligent Email Processing System")
    
    parser.add_argument('--max-emails', type=int, default=20,
                       help='Maximum emails to process per label (default: 20)')
    
    parser.add_argument('--labels', nargs='+',
                       help='Specific Gmail labels to process (default: all configured labels)')
    
    parser.add_argument('--time-filter', type=str,
                       help='Time filter (e.g., "7d", "24h", "after:2024/01/01")')
    
    parser.add_argument('--quick', action='store_true',
                       help='Quick run with 5 emails max')
    
    args = parser.parse_args()
    
    if args.quick:
        print("⚡ QUICK RUN MODE - Processing max 5 emails")
        asyncio.run(run_with_options(max_emails=5))
    elif args.labels or args.time_filter or args.max_emails != 20:
        asyncio.run(run_with_options(
            max_emails=args.max_emails,
            labels=args.labels,
            time_filter=args.time_filter
        ))
    else:
        asyncio.run(run_intelligent_processing())

if __name__ == "__main__":
    main()