"""
System-level prompts for MistralAI service configuration.
"""

from typing import Dict, Any
from .base_prompts import BasePrompts, PromptComponent


class SystemPrompts(BasePrompts):
    """System-level prompts for MistralAI configuration."""
    
    # Core system prompt for MistralAI
    MISTRAL_SYSTEM_PROMPT = PromptComponent(
        name="mistral_system_prompt",
        content="""You are a JSON extraction tool. You MUST respond with ONLY valid JSON - no explanations, no thinking, no markdown, no additional text.

CRITICAL: Do not use <think> tags or any reasoning. Respond with pure JSON only.

Extract order information and return it in this exact JSON format:
{
    "customer_details": {
        "debtor_id": 0,
        "customer_order_number": "order_number",
        "customer_name": "customer_name", 
        "delivery_address": "address",
        "shipping_method": "BEST WAY"
    },
    "order_lines": [
        {
            "stockcode": "code",
            "orderquantity": 1
        }
    ],
    "order_status": 0
}

Rules:
- Team Systems is the supplier, never the customer
- If no customer found, use "UNKNOWN CUSTOMER" and debtor_id 0
- Remove whitespace from stock codes
- Use "BEST WAY" if shipping method empty""",
        category="system",
        priority=10
    )
    
    # JSON mode enforcement for strict output
    JSON_MODE_ENFORCEMENT = PromptComponent(
        name="json_mode_enforcement",
        content="""You are a JSON extraction tool. You MUST respond with ONLY a valid JSON object.

CRITICAL INSTRUCTIONS:
- You MUST respond with ONLY a valid JSON object
- Do NOT include any explanations, thinking process, or markdown formatting
- Do NOT use <think> tags or any reasoning text
- Do NOT include any text before or after the JSON
- Return pure JSON only""",
        category="json_mode",
        priority=10
    )
    
    # Fallback prompt for non-JSON mode
    FALLBACK_PROMPT_ENHANCEMENT = PromptComponent(
        name="fallback_prompt_enhancement",
        content="""CRITICAL: You are a JSON extraction tool. You MUST respond with ONLY valid JSON.

IMPORTANT RULES:
1. Respond with ONLY a JSON object
2. Do NOT use <think> tags
3. Do NOT include explanations
4. Do NOT use markdown formatting
5. Start your response with { and end with }

JSON OUTPUT ONLY:""",
        category="fallback",
        priority=10
    )
    
    # Error handling prompts
    ERROR_RECOVERY_PROMPT = PromptComponent(
        name="error_recovery_prompt",
        content="""The previous response was not valid JSON. Please provide ONLY a valid JSON object with no additional text.

Requirements:
- Start with {
- End with }
- Valid JSON syntax
- No explanations
- No markdown
- No <think> tags""",
        category="error_recovery",
        priority=10
    )
    
    @classmethod
    def get_mistral_system_prompt(cls) -> str:
        """Get the main system prompt for MistralAI."""
        return cls.MISTRAL_SYSTEM_PROMPT.content
    
    @classmethod
    def get_json_enforcement_prompt(cls) -> str:
        """Get JSON enforcement prompt for strict mode."""
        return cls.JSON_MODE_ENFORCEMENT.content
    
    @classmethod
    def enhance_prompt_for_json_strict(cls, base_prompt: str) -> str:
        """Enhance any prompt with strict JSON enforcement."""
        json_prefix = cls.JSON_MODE_ENFORCEMENT.content
        json_suffix = "\n\nCRITICAL: You MUST only output a single, valid JSON object. Do not include any other text, explanations, or markdown formatting."
        
        return f"{json_prefix}\n\n{base_prompt}{json_suffix}"
    
    @classmethod
    def enhance_prompt_for_json_fallback(cls, base_prompt: str) -> str:
        """Enhance prompt for fallback non-JSON mode."""
        return f"{cls.FALLBACK_PROMPT_ENHANCEMENT.content}\n\n{base_prompt}"
    
    @classmethod
    def get_error_recovery_prompt(cls, original_prompt: str, error_details: str = "") -> str:
        """Get error recovery prompt for failed JSON parsing."""
        error_context = f"\nError details: {error_details}" if error_details else ""
        
        return f"""{cls.ERROR_RECOVERY_PROMPT.content}

{error_context}

Original request:
{original_prompt}

Please provide the response as valid JSON only:"""
    
    @classmethod
    def build_analysis_prompt(cls, email_content: str) -> str:
        """Build prompt for email analysis."""
        return f"""Analyze this email and determine if it contains a purchase order or order-related information.

Email content:
{email_content}

Return a JSON object with:
- is_order: boolean indicating if this is an order
- confidence: float between 0 and 1
- order_type: type of order (purchase_order, sales_order, etc.)
- summary: brief summary of the order content"""
    
    @classmethod
    def build_extraction_prompt(cls, email_content: str) -> str:
        """Build prompt for order data extraction."""
        return f"""Extract structured order data from this email.

Email content:
{email_content}

Return a JSON object with:
- customer_details: object with debtor_id (use 0 if unknown), customer_name, email
- purchase_order_number: string
- line_items: array of objects with item_code, description, quantity, unit_price
- delivery_address: object with street, city, state, postal_code, country
- special_instructions: string
- total_amount: float
- currency: string (default to "AUD")"""