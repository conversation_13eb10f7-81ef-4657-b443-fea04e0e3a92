"""
Email summary and analysis prompts.
"""

from typing import Dict, Any
from .base_prompts import BasePrompts, PromptComponent


class SummaryPrompts(BasePrompts):
    """Email summary and analysis prompts."""
    
    # Email analysis system prompt
    EMAIL_ANALYSIS_SYSTEM = PromptComponent(
        name="email_analysis_system",
        content="""You are an email analysis specialist for Team Systems, expert at categorizing emails and determining required actions.""",
        category="summary_system",
        priority=10
    )
    
    # Email intent recognition
    INTENT_RECOGNITION = PromptComponent(
        name="intent_recognition",
        content="""EMAIL INTENT RECOGNITION:
- Prioritize accurate email_intent recognition using keywords:
  * "quote" or "quotation" → Quote Request
  * "price" or "pricing" → Price Inquiry  
  * "stock" or "availability" → Stock Check
  * "purchase order" or "PO" → Purchase Order
  * "general" or no clear intent → General Inquiry
- Look for urgency indicators: "urgent", "ASAP", "immediate"
- Identify if response is required and timeline""",
        category="summary_rules",
        priority=9
    )
    
    # Summary structure rules
    SUMMARY_STRUCTURE = PromptComponent(
        name="summary_structure",
        content="""SUMMARY STRUCTURE REQUIREMENTS:
- Provide concise, actionable summary (2-3 sentences max)
- Identify key stakeholders (sender, recipients, mentioned parties)
- Extract critical dates, deadlines, or timelines
- Note any attachments and their relevance
- Flag any special instructions or requirements""",
        category="summary_rules",
        priority=8
    )
    
    # Markdown formatting rules
    MARKDOWN_RULES = PromptComponent(
        name="markdown_rules",
        content="""MARKDOWN FORMATTING RULES:
- Use clear headers (##) for main sections
- Use bullet points (-) for lists and key information
- Use **bold** for important details like PO numbers, deadlines
- Use tables for structured data when appropriate
- Include email metadata (From, To, Date, Subject)
- Separate order details from general content""",
        category="format_rules",
        priority=7
    )
    
    # Email summary JSON template
    EMAIL_SUMMARY_TEMPLATE = PromptComponent(
        name="email_summary_template",
        content="""Required JSON format for email summary:
{
    "summary": "Brief 2-3 sentence summary of email content",
    "intent": "Quote Request|Price Inquiry|Stock Check|Purchase Order|General Inquiry",
    "urgency": "Low|Medium|High|Critical",
    "action_required": "Response needed|Information only|Order processing|Follow-up required",
    "key_details": {
        "sender_type": "Customer|Colleague|Supplier|Unknown",
        "contains_order": true/false,
        "has_attachments": true/false,
        "deadline": "YYYY-MM-DD or null",
        "po_number": "PO number if found or null"
    },
    "next_steps": ["List of recommended actions"]
}""",
        category="template",
        priority=10
    )
    
    @classmethod
    def build_email_summary_prompt(cls, email_body: str, subject: str, sender: str = "") -> str:
        """Build complete email summary prompt."""
        
        system_section = cls.EMAIL_ANALYSIS_SYSTEM.content
        json_enforcement = cls.JSON_ENFORCEMENT.content
        company_context = cls.COMPANY_CONTEXT.content
        email_rules = cls.EMAIL_RULES.content
        intent_recognition = cls.INTENT_RECOGNITION.content
        summary_structure = cls.SUMMARY_STRUCTURE.content
        json_template = cls.EMAIL_SUMMARY_TEMPLATE.content
        
        prompt = f"""{system_section}

{json_enforcement}

{company_context}

{email_rules}

{intent_recognition}

{summary_structure}

Email to analyze:
Subject: {subject}
From: {sender}
Content: {email_body}

{json_template}"""
        
        return prompt
    
    @classmethod
    def build_markdown_summary_prompt(cls, email_body: str, subject: str, sender: str, pdf_content: str = "") -> str:
        """Build markdown summary generation prompt."""
        
        system_section = "You are a markdown document generator, expert at creating structured summaries of email and document content."
        company_context = cls.COMPANY_CONTEXT.content
        markdown_rules = cls.MARKDOWN_RULES.content
        summary_structure = cls.SUMMARY_STRUCTURE.content
        
        content_section = f"""Email Content:
Subject: {subject}
From: {sender}
Body: {email_body}"""
        
        if pdf_content:
            content_section += f"\n\nPDF Content:\n{pdf_content}"
        
        prompt = f"""{system_section}

{company_context}

{summary_structure}

{markdown_rules}

Create a structured markdown summary of the following email and document content:

{content_section}

Generate a comprehensive markdown document that includes:
- Email metadata and summary
- Key order information (if present)
- Important details and requirements
- Action items and next steps
- Relevant attachments summary"""
        
        return prompt
    
    @classmethod
    def build_intent_classification_prompt(cls, email_content: str) -> str:
        """Build prompt for email intent classification."""
        
        return f"""{cls.EMAIL_ANALYSIS_SYSTEM.content}

{cls.JSON_ENFORCEMENT.content}

{cls.INTENT_RECOGNITION.content}

Classify the intent of this email:

{email_content}

Return classification as JSON:
{{
    "primary_intent": "Quote Request|Price Inquiry|Stock Check|Purchase Order|General Inquiry|Order Confirmation",
    "confidence": 0.0-1.0,
    "secondary_intents": ["list of other possible intents"],
    "keywords_found": ["list of keywords that influenced classification"],
    "requires_action": true/false
}}"""