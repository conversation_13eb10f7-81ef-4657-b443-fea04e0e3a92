"""
Test the enhanced LLM service with centralized prompts.
"""

import asyncio
from llm_service.main import LLMService

async def test_llm_service():
    print('🚀 Testing Enhanced LLM Service with Centralized Prompts')
    print('=' * 60)
    
    try:
        async with LLMService() as llm_service:
            # Test health check
            print('📊 Testing Health Check...')
            health = await llm_service.health_check()
            print(f'   Health Status: {health}')
            
            # Test order extraction with sample content
            sample_content = '''
            Purchase Order #PO-12345
            From: Gateway Packaging
            Ship to: 123 Main Street, Sydney NSW 2000
            
            Items:
            - 10x WIDGET-A (Material handling equipment)
            - 5x CONVEYOR-B (Belt conveyor system)
            
            Special Instructions: Use customers carrier for delivery
            '''
            
            print('\n📦 Testing Order Extraction...')
            result = await llm_service.extract_order_from_content(sample_content)
            
            if result:
                print('✅ Order extraction successful!')
                customer_name = result.get('customer_details', {}).get('customer_name', 'Unknown')
                po_number = result.get('customer_details', {}).get('customer_order_number', 'Unknown')
                order_lines = len(result.get('order_lines', []))
                
                print(f'   📋 Customer: {customer_name}')
                print(f'   📋 PO Number: {po_number}')
                print(f'   📋 Order Lines: {order_lines}')
                
                # Show business rules integration
                shipping_method = result.get('customer_details', {}).get('shipping_method', 'Unknown')
                print(f'   🚚 Shipping Method: {shipping_method}')
                
            else:
                print('⚠️ No order data extracted')
            
            print('\n🎉 LLM Service Integration Test Complete!')
                
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_llm_service())