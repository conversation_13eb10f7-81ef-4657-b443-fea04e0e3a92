"""
CLI interface for the Email Order Processor.
Handles command-line arguments, execution modes, and workflow orchestration.
"""
import logging
import os
import argparse
import time
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional

from main_processor import EmailOrderProcessor
from reporting import ReportingService
from gmail_service import GmailService

logger = logging.getLogger(__name__)


def parse_time_filter(time_spec: str) -> str:
    """Convert time specification to Gmail search query format."""
    if not time_spec:
        return ""
    
    # Handle relative time specifications
    if time_spec.endswith('d'):  # days
        days = int(time_spec[:-1])
        date = (datetime.now() - timedelta(days=days)).strftime('%Y/%m/%d')
        return f"after:{date}"
    elif time_spec.endswith('h'):  # hours
        hours = int(time_spec[:-1])
        # Gmail doesn't support hour precision, convert to days
        days = max(1, hours // 24)
        date = (datetime.now() - timedelta(days=days)).strftime('%Y/%m/%d')
        return f"after:{date}"
    elif time_spec.endswith('w'):  # weeks
        weeks = int(time_spec[:-1])
        days = weeks * 7
        date = (datetime.now() - timedelta(days=days)).strftime('%Y/%m/%d')
        return f"after:{date}"
    else:
        # Assume it's already in Gmail format (e.g., "after:2024/01/01")
        return time_spec


def build_enhanced_query(args, time_query: Optional[str]) -> str:
    """Build enhanced Gmail query with all filters."""
    query_parts = []
    
    if time_query:
        query_parts.append(time_query)
    
    if args.unread_only:
        query_parts.append("is:unread")
    
    if args.custom_query:
        query_parts.append(args.custom_query)
    
    return " ".join(query_parts)


def perform_dry_run(processor: EmailOrderProcessor, args, enhanced_query: str, labels_to_process: List[str]):
    """Perform a dry run to show what would be processed."""
    print("\n" + "="*60)
    print("DRY RUN - Preview of emails that would be processed")
    print("="*60)
    
    try:
        gmail_service = processor.gmail_service
        service = getattr(gmail_service, "service", None)
        
        if not service:
            print("ERROR: Gmail service not initialized")
            return []
        
        total_found = 0
        for label_name in labels_to_process:
            label_id = gmail_service.get_label_id(label_name)
            if label_id:
                list_params = {
                    'userId': 'me',
                    'labelIds': [label_id],
                    'maxResults': args.max_emails
                }
                if enhanced_query:
                    list_params['q'] = enhanced_query
                
                response = service.users().messages().list(**list_params).execute()
                messages = response.get('messages', [])
                
                print(f"\nLabel: {label_name}")
                print(f"  Found: {len(messages)} emails")
                
                for i, msg_ref in enumerate(messages[:5]):  # Show first 5 as preview
                    try:
                        email_data = gmail_service._get_email_details(msg_ref['id'], label_name)
                        if email_data:
                            print(f"  {i+1}. {email_data.subject[:50]}... (from: {email_data.sender[:30]})")
                    except Exception as e:
                        print(f"  {i+1}. Error fetching email details: {e}")
                
                if len(messages) > 5:
                    print(f"  ... and {len(messages) - 5} more emails")
                
                total_found += len(messages)
        
        print(f"\nTotal emails that would be processed: {total_found}")
        print("="*60)
        
    except Exception as e:
        print(f"ERROR during dry run: {e}")
    
    return []


def process_specific_emails(processor: EmailOrderProcessor, args):
    """Process specific email IDs."""
    print(f"Processing {len(args.email_ids)} specific emails...")
    # This would need to be implemented in the processor
    # For now, return empty list
    logger.warning("Specific email ID processing not yet implemented")
    return []


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Email Order Processor - Extract order data from Gmail emails",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run once with default settings
  python cli.py --once
  
  # Process specific labels with more emails
  python cli.py --once --labels Brady RSEA --max-emails 10
  
  # Process last 7 days of emails
  python cli.py --once --time-filter 7d
  
  # Continuous polling every 10 minutes
  python cli.py --poll-interval 10
  
  # Process last 100 emails from all labels
  python cli.py --once --max-emails 100
  
  # Process emails from specific date range
  python cli.py --once --time-filter "after:2024/01/01 before:2024/01/31"
  
  # Custom output directory and dry run
  python cli.py --once --output-dir /custom/path --dry-run
  
  # Process only unread emails with custom query
  python cli.py --once --unread-only --custom-query "subject:urgent"
  
  # Export results to CSV and skip Brady packing slips
  python cli.py --once --export-csv --include-brady-packing
  
  # Process specific email IDs
  python cli.py --email-ids 18a1b2c3d4e5f6g7 18h1i2j3k4l5m6n7
        """
    )
    
    # Execution mode
    parser.add_argument('--once', action='store_true',
                       help='Run once instead of continuous polling')
    
    parser.add_argument('--poll-interval', type=int, default=5,
                       help='Polling interval in minutes for continuous mode (default: 5)')
    
    parser.add_argument('--dry-run', action='store_true',
                       help='Preview what would be processed without actually processing emails')
    
    parser.add_argument('--list-labels', action='store_true',
                       help='List all available Gmail labels and exit')
    
    # Email filtering
    parser.add_argument('--labels', nargs='+', 
                       default=['Brady', 'RSEA', 'Woolworths', 'Brierley', 'Gateway', 'Highgate', 'Sitecraft'],
                       help='Gmail labels to process (default: all configured labels)')
    
    parser.add_argument('--exclude-labels', nargs='+', default=[],
                       help='Labels to exclude from processing')
    
    parser.add_argument('--max-emails', type=int, default=5,
                       help='Maximum emails to process per label (default: 5)')
    
    parser.add_argument('--max-total-emails', type=int,
                       help='Maximum total emails to process across all labels')
    
    parser.add_argument('--time-filter', type=str,
                       help='Time filter for emails. Examples: "7d" (7 days), "2w" (2 weeks), "24h" (24 hours), or Gmail format like "after:2024/01/01"')
    
    parser.add_argument('--unread-only', action='store_true',
                       help='Process only unread emails')
    
    parser.add_argument('--custom-query', type=str,
                       help='Additional Gmail search query to append (e.g., "subject:urgent")')
    
    parser.add_argument('--email-ids', nargs='+',
                       help='Process specific email IDs instead of searching by labels')
    
    parser.add_argument('--include-brady-packing', action='store_true',
                       help='Include Brady packing slip emails (normally skipped)')
    
    parser.add_argument('--review-all-emails', action='store_true',
                       help='Review ALL emails in label (not just those with PDF attachments)')
    
    # Output options
    parser.add_argument('--output-dir', type=str, default='.',
                       help='Base output directory for markdown and myob folders (default: current directory)')
    
    parser.add_argument('--markdown-only', action='store_true',
                       help='Generate only markdown summaries, skip MYOB payloads')
    
    parser.add_argument('--myob-only', action='store_true',
                       help='Generate only MYOB payloads, skip markdown summaries')
    
    parser.add_argument('--no-files', action='store_true',
                       help='Skip saving files to disk (useful for testing)')
    
    parser.add_argument('--export-csv', action='store_true',
                       help='Export processing results to CSV file')
    
    parser.add_argument('--export-json', action='store_true',
                       help='Export processing results to JSON file')
    
    # Email and notification options
    parser.add_argument('--no-email', action='store_true',
                       help='Skip sending completion emails')
    
    parser.add_argument('--email-recipient', type=str,
                       help='Override default email recipient for notifications')
    
    parser.add_argument('--email-subject-prefix', type=str, default='',
                       help='Add prefix to notification email subjects')
    
    # Processing options
    parser.add_argument('--no-labeling', action='store_true',
                       help='Skip adding processing labels to Gmail emails')
    
    parser.add_argument('--force-reprocess', action='store_true',
                       help='Reprocess emails even if they have "Processed" label')
    
    parser.add_argument('--skip-pdf', action='store_true',
                       help='Skip PDF attachment processing')
    
    parser.add_argument('--parallel', type=int, default=1,
                       help='Number of parallel processing threads (default: 1)')
    
    # Logging and debugging
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug logging (most verbose)')
    
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Suppress console output (errors only)')
    
    parser.add_argument('--log-file', type=str,
                       help='Write logs to specified file')
    
    parser.add_argument('--stats', action='store_true',
                       help='Show detailed processing statistics')
    
    # Configuration
    parser.add_argument('--config-file', type=str,
                       help='Use custom configuration file')
    
    parser.add_argument('--list-config', action='store_true',
                       help='Show current configuration and exit')
    
    return parser.parse_args()


def setup_logging(args):
    """Setup logging based on CLI arguments."""
    if args.debug:
        level = logging.DEBUG
    elif args.verbose:
        level = logging.INFO
    elif args.quiet:
        level = logging.ERROR
    else:
        level = logging.WARNING
    
    # Configure root logger
    logging.getLogger().setLevel(level)
    
    # Setup file logging if specified
    if args.log_file:
        file_handler = logging.FileHandler(args.log_file)
        file_handler.setLevel(level)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        logging.getLogger().addHandler(file_handler)
        logger.info(f"Logging to file: {args.log_file}")


def list_gmail_labels():
    """List all available Gmail labels."""
    try:
        gmail_service = GmailService()
        service = getattr(gmail_service, "service", None)
        if not service:
            print("ERROR: Gmail service not initialized")
            return
        
        results = service.users().labels().list(userId='me').execute()
        labels = results.get('labels', [])
        
        print("\nAvailable Gmail Labels:")
        print("=" * 50)
        for label in sorted(labels, key=lambda x: x['name']):
            label_type = label.get('type', 'user')
            print(f"  {label['name']} (ID: {label['id']}, Type: {label_type})")
        print(f"\nTotal: {len(labels)} labels")
        
    except Exception as e:
        print(f"ERROR: Failed to list labels: {e}")


def show_config():
    """Show current configuration."""
    print("\nCurrent Configuration:")
    print("=" * 50)
    print(f"Default Labels: Brady, RSEA, Woolworths, Brierley, Gateway, Highgate, Sitecraft")
    print(f"Default Max Emails: 40 per label")
    print(f"Default Poll Interval: 5 minutes")
    print(f"Default Output Directory: current directory")
    
    # Show environment variables
    env_vars = ['LOG_RECIPIENT_EMAIL', 'USER_EMAIL', 'OPENAI_API_KEY']
    print("\nEnvironment Variables:")
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if 'KEY' in var or 'TOKEN' in var:
                masked = value[:8] + '*' * (len(value) - 8) if len(value) > 8 else '*' * len(value)
                print(f"  {var}: {masked}")
            else:
                print(f"  {var}: {value}")
        else:
            print(f"  {var}: Not set")


async def run_single_cycle(args, processor: EmailOrderProcessor, reporting_service: ReportingService):
    """Run a single processing cycle."""
    try:
        # Build enhanced query with new options
        time_query = parse_time_filter(args.time_filter) if args.time_filter else None
        enhanced_query = build_enhanced_query(args, time_query)
        
        # Filter labels
        labels_to_process = args.labels
        if args.exclude_labels:
            labels_to_process = [label for label in labels_to_process if label not in args.exclude_labels]
        
        if args.dry_run:
            # Dry run mode - just show what would be processed
            return perform_dry_run(processor, args, enhanced_query, labels_to_process)
        
        # Actual processing
        if args.email_ids:
            processed_orders = process_specific_emails(processor, args)
        else:
            processed_orders = await processor.run_extraction_workflow(
                label_names=labels_to_process,
                max_emails=args.max_emails,
                time_filter=enhanced_query,
                review_mode=args.review_all_emails
            )
        
        # Apply total email limit if specified
        if args.max_total_emails and len(processed_orders) > args.max_total_emails:
            processed_orders = processed_orders[:args.max_total_emails]
            if not args.quiet:
                print(f"Limited to {args.max_total_emails} total emails as requested")
        
        # Export results if requested
        if args.export_csv:
            reporting_service.export_results_csv(processed_orders)
        
        if args.export_json:
            reporting_service.export_results_json(processed_orders)
        
        # Show statistics if requested
        if args.stats:
            reporting_service.show_processing_stats(processed_orders)
        
        # Send completion email with custom recipient/subject if specified
        if not args.no_email:
            subject = f"{args.email_subject_prefix}Email Extraction Complete - TeamsysV0.1"
            reporting_service.send_completion_email(
                subject=subject,
                processed_orders=processed_orders,
                recipient=args.email_recipient
            )
        else:
            logger.info("Skipping completion email as requested")
        
        return processed_orders
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        if not args.quiet:
            print(f"ERROR: {e}")
        
        # Send error notification email
        if not args.no_email:
            try:
                subject = f"{args.email_subject_prefix}Email Extraction Failed - TeamsysV0.1"
                reporting_service.send_error_notification(
                    subject=subject,
                    error=str(e),
                    recipient=args.email_recipient
                )
            except Exception as email_error:
                logger.error(f"Failed to send error notification email: {email_error}")
        
        return []


def main():
    """Main entry point for the CLI application."""
    args = parse_arguments()
    
    # Handle special commands that exit immediately
    if args.list_labels:
        list_gmail_labels()
        return
    
    if args.list_config:
        show_config()
        return
    
    # Setup logging
    setup_logging(args)
    
    # Validate arguments
    if args.markdown_only and args.myob_only:
        print("ERROR: Cannot specify both --markdown-only and --myob-only")
        return
    
    if args.email_ids and args.labels != ['Brady', 'RSEA', 'Woolworths', 'Brierley', 'Gateway', 'Highgate', 'Sitecraft']:
        print("WARNING: --email-ids specified, ignoring --labels option")
    
    # Setup output directories
    if args.output_dir != '.':
        os.makedirs(args.output_dir, exist_ok=True)
        os.makedirs(os.path.join(args.output_dir, 'markdown'), exist_ok=True)
        os.makedirs(os.path.join(args.output_dir, 'myob'), exist_ok=True)
    
    # Filter labels
    labels_to_process = args.labels
    if args.exclude_labels:
        labels_to_process = [label for label in labels_to_process if label not in args.exclude_labels]
    
    if not args.quiet:
        print(f"\n{'='*60}")
        print("EMAIL ORDER PROCESSOR (Extraction Only)")
        print(f"{'='*60}")
        
        if args.dry_run:
            print(">> DRY RUN MODE - No actual processing will occur")
        elif args.once:
            print(">> Running in SINGLE RUN mode")
        else:
            print(f">> Running in CONTINUOUS mode (polling every {args.poll_interval} minutes)")
        
        if args.email_ids:
            print(f">> Processing specific email IDs: {len(args.email_ids)} emails")
        else:
            print(f">> Processing labels: {', '.join(labels_to_process)}")
            print(f">> Max emails per label: {args.max_emails}")
            if args.max_total_emails:
                print(f">> Max total emails: {args.max_total_emails}")
        
        if args.time_filter:
            time_query = parse_time_filter(args.time_filter)
            print(f">> Time filter: {args.time_filter} -> {time_query}")
        else:
            print(">> No time filter applied")
        
        if args.unread_only:
            print(">> Processing UNREAD emails only")
        
        if args.review_all_emails:
            print(">> REVIEW ALL EMAILS mode - processing every email (not just PDFs)")
        
        if args.custom_query:
            print(f">> Custom query: {args.custom_query}")
        
        if args.output_dir != '.':
            print(f">> Output directory: {args.output_dir}")
        
        print(f"{'='*60}")
    
    # Initialize services
    processor = EmailOrderProcessor()
    reporting_service = ReportingService()
    
    if args.once:
        # Single run mode
        processed_orders = asyncio.run(run_single_cycle(args, processor, reporting_service))
        print(f"\nSingle run completed. Processed {len(processed_orders)} orders.")
    else:
        # Continuous polling mode
        poll_seconds = args.poll_interval * 60
        
        try:
            while True:
                processed_orders = asyncio.run(run_single_cycle(args, processor, reporting_service))
                print(f"\nWaiting {args.poll_interval} minutes before next poll...")
                logger.info(f"Waiting {args.poll_interval} minutes before next poll...")
                time.sleep(poll_seconds)
        except KeyboardInterrupt:
            print("\nPolling stopped by user")
            logger.info("Polling stopped by user interrupt")


if __name__ == "__main__":
    main()