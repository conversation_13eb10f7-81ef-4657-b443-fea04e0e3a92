"""Memory service for Supabase operations."""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from enhanced_supabase_memory_client import get_enhanced_memory_client
from tenacity import retry, stop_after_attempt, wait_exponential

from ..core.base import BaseService
from ..core.exceptions import MemoryServiceException
from ..core.constants import DEFAULT_MAX_RETRIES

logger = logging.getLogger(__name__)


class MemoryService(BaseService):
    """Service for interacting with Supabase memory database."""
    
    def __init__(self, **kwargs):
        super().__init__(kwargs)
        self.memory_client = get_enhanced_memory_client()
    
    async def health_check(self) -> bool:
        """Check if the memory service is healthy."""
        try:
            # Simple health check - try to get a small amount of context
            context = self.memory_client.get_relevant_context(
                query="health_check",
                limit=1,
                use_semantic_search=True
            )
            return context is not None
        except Exception as e:
            logger.error(f"Memory service health check failed: {str(e)}")
            return False
    
    @retry(
        stop=stop_after_attempt(DEFAULT_MAX_RETRIES),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def get_relevant_context(
        self,
        query: str,
        n_results: int = 5,
        use_semantic_search: bool = True
    ) -> str:
        """Query the memory database for relevant context."""
        try:
            context = self.memory_client.get_relevant_context(
                query=query,
                limit=n_results,
                use_semantic_search=use_semantic_search
            )
            
            logger.info(f"Retrieved context for query: {query[:50]}...")
            return context
            
        except Exception as e:
            logger.error(f"Error querying memory database: {str(e)}")
            raise MemoryServiceException(
                f"Failed to retrieve context: {str(e)}",
                details={"query": query, "n_results": n_results}
            )
    
    @retry(
        stop=stop_after_attempt(DEFAULT_MAX_RETRIES),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def store_order_in_memory(
        self,
        order_data: Dict[str, Any],
        email_id: Optional[str] = None
    ) -> bool:
        """Store processed order data in memory."""
        try:
            success = self.memory_client.store_order_in_memory(
                order_data=order_data,
                email_id=email_id
            )
            
            if success:
                logger.info(f"Stored order in Supabase memory database")
            else:
                logger.error("Failed to store order in memory")
            
            return success
            
        except Exception as e:
            logger.error(f"Error storing order in memory: {str(e)}")
            raise MemoryServiceException(
                f"Failed to store order: {str(e)}",
                details={"order_data": order_data, "email_id": email_id}
            )
    
    def store_context(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Store additional context in memory."""
        try:
            # Implementation depends on memory client capabilities
            return True
        except Exception as e:
            logger.error(f"Error storing context: {str(e)}")
            return False