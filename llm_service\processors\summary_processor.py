"""Summary processor for email and PDF content."""

import json
import logging
from typing import Dict, Any

from ..models.schemas import <PERSON>ailData, EmailSummary
from ..services.mistral_service import MistralService
from ..services.memory_service import MemoryService
from ..prompts.prompt_manager import prompt_manager

logger = logging.getLogger(__name__)


class SummaryProcessor:
    """Processor for generating summaries of email and PDF content."""
    
    def __init__(self, mistral_service: MistralService, memory_service: MemoryService):
        self.mistral_service = mistral_service
        self.memory_service = memory_service
    
    async def generate_email_summary(self, email_body: str, subject: str) -> EmailSummary:
        """Generate a short summary and determine action required for an email."""
        # Use centralized prompt manager for email summary
        prompt = prompt_manager.get_email_summary_prompt(email_body, subject)
        
        try:
            response = await self.mistral_service.generate_content(prompt)
            
            # Clean the response
            cleaned_response = response.text.strip().replace("```json", "").replace("```", "")
            
            try:
                summary_data = json.loads(cleaned_response)
                return EmailSummary(
                    summary=summary_data.get("summary", "Summary not available"),
                    action_required=summary_data.get("action_required", "Action not determined")
                )
            except json.JSONDecodeError:
                logger.error(f"Failed to decode JSON from LLM response: {cleaned_response}")
                return EmailSummary(
                    summary="Could not parse LLM summary.",
                    action_required="Manual review required."
                )
                
        except Exception as e:
            logger.error(f"Error generating email summary: {str(e)}")
            return EmailSummary(
                summary="Error during summary generation.",
                action_required="Error, manual review needed."
            )
    
    async def generate_markdown_summary(
        self,
        email_data: EmailData,
        pdf_content: str = ""
    ) -> str:
        """Generate a structured markdown summary of the email and PDF content."""
        
        # Use centralized prompt manager for markdown summary
        prompt = prompt_manager.get_markdown_summary_prompt(
            email_data.body, 
            email_data.subject, 
            email_data.sender, 
            pdf_content
        )
        
        try:
            response = await self.mistral_service.generate_content(prompt)
            return response.text if hasattr(response, 'text') else "Failed to generate summary"
            
        except Exception as e:
            logger.error(f"Error generating markdown summary: {str(e)}")
            return f"Error generating summary: {str(e)}"