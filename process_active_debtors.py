#!/usr/bin/env python3
"""
Process the active_debtors.xlsx file and analyze its structure for Supabase upload.
"""
import pandas as pd
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_active_debtors_file(file_path: str) -> Optional[pd.DataFrame]:
    """Read and analyze the active debtors Excel file."""
    try:
        logger.info(f"Reading active debtors file: {file_path}")
        
        # Try reading the Excel file
        df = pd.read_excel(file_path)
        logger.info(f"Successfully read Excel file with {len(df)} rows and {len(df.columns)} columns")
        
        # Display basic info about the file
        logger.info(f"Columns: {list(df.columns)}")
        logger.info(f"Data types:")
        for col in df.columns:
            logger.info(f"  {col}: {df[col].dtype}")
        
        logger.info(f"First 5 rows:")
        print(df.head())
        
        # Check for null values
        logger.info(f"Null values per column:")
        for col in df.columns:
            null_count = df[col].isnull().sum()
            if null_count > 0:
                logger.info(f"  {col}: {null_count} nulls")
        
        # Look for key columns we need for Supabase
        required_columns = ['debtor_id', 'customer_name', 'account_name']
        found_columns = []
        
        for col in df.columns:
            col_lower = col.lower()
            if any(req in col_lower for req in ['debtor', 'id', 'account']):
                found_columns.append(col)
                logger.info(f"Potential ID column: {col}")
            elif any(req in col_lower for req in ['name', 'customer', 'account']):
                found_columns.append(col)
                logger.info(f"Potential name column: {col}")
        
        return df
        
    except Exception as e:
        logger.error(f"Error reading Excel file: {e}")
        return None

def prepare_supabase_data(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Prepare the data for Supabase upload."""
    try:
        logger.info("Preparing data for Supabase upload...")
        
        supabase_records = []
        
        for index, row in df.iterrows():
            # Map the Excel columns to Supabase schema
            # We'll need to identify the correct column mappings
            record = {
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            # Try to map columns (this will need adjustment based on actual column names)
            for col in df.columns:
                col_lower = col.lower()
                value = row[col]
                
                # Skip null values
                if pd.isna(value):
                    continue
                
                # Map to Supabase schema
                if 'debtor' in col_lower and ('id' in col_lower or 'no' in col_lower):
                    record['debtor_id'] = int(value) if pd.notna(value) else None
                elif 'account' in col_lower and 'name' in col_lower:
                    record['customer_name'] = str(value).strip() if pd.notna(value) else None
                elif 'name' in col_lower and 'customer' not in col_lower:
                    record['customer_name'] = str(value).strip() if pd.notna(value) else None
                elif 'email' in col_lower:
                    record['email'] = str(value).strip() if pd.notna(value) else None
                elif 'phone' in col_lower:
                    record['phone'] = str(value).strip() if pd.notna(value) else None
                elif 'address' in col_lower:
                    if 'address_line1' not in record:
                        record['address_line1'] = str(value).strip() if pd.notna(value) else None
                elif 'contact' in col_lower:
                    record['contact_person'] = str(value).strip() if pd.notna(value) else None
            
            # Only add records that have both debtor_id and customer_name
            if record.get('debtor_id') and record.get('customer_name'):
                supabase_records.append(record)
            else:
                logger.debug(f"Skipping row {index}: missing debtor_id or customer_name")
        
        logger.info(f"Prepared {len(supabase_records)} records for Supabase upload")
        return supabase_records
        
    except Exception as e:
        logger.error(f"Error preparing Supabase data: {e}")
        return []

def show_sample_records(records: List[Dict[str, Any]], count: int = 5):
    """Show sample records for verification."""
    logger.info(f"Sample records (first {count}):")
    for i, record in enumerate(records[:count]):
        logger.info(f"Record {i+1}:")
        for key, value in record.items():
            if value is not None:
                logger.info(f"  {key}: {value}")
        logger.info("")

def main():
    """Main function to process the active debtors file."""
    file_path = r"C:\Users\<USER>\Desktop\active_debtors.xlsx"
    
    print("ACTIVE DEBTORS FILE ANALYSIS")
    print("=" * 60)
    
    # Read and analyze the file
    df = analyze_active_debtors_file(file_path)
    if df is None:
        return
    
    print("\n" + "=" * 60)
    print("SUPABASE DATA PREPARATION")
    print("=" * 60)
    
    # Prepare data for Supabase
    supabase_records = prepare_supabase_data(df)
    
    if supabase_records:
        show_sample_records(supabase_records)
        
        print(f"\n" + "=" * 60)
        print("SUMMARY")
        print("=" * 60)
        print(f"Total rows in Excel: {len(df)}")
        print(f"Records ready for Supabase: {len(supabase_records)}")
        print(f"Success rate: {len(supabase_records)/len(df)*100:.1f}%")
        
        # Show unique debtor_ids count
        unique_debtors = len(set(r['debtor_id'] for r in supabase_records if r.get('debtor_id')))
        print(f"Unique debtor IDs: {unique_debtors}")
        
        # Check for Woolworths entries
        woolworths_records = [r for r in supabase_records if 'woolworth' in r.get('customer_name', '').lower()]
        print(f"Woolworths-related records: {len(woolworths_records)}")
        
        if woolworths_records:
            print("Woolworths entries found:")
            for record in woolworths_records[:3]:
                print(f"  ID: {record['debtor_id']} - {record['customer_name']}")
    else:
        print("No valid records prepared for Supabase upload")

if __name__ == "__main__":
    main()