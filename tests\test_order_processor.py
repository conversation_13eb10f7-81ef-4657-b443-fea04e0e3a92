"""Tests for order processor."""

import pytest
from unittest.mock import Mock, patch
from llm_service.processors.order_processor import OrderProcessor
from llm_service.services.mistral_service import MistralService
from llm_service.services.memory_service import MemoryService
from llm_service.core.exceptions import ValidationException


class TestOrderProcessor:
    """Tests for OrderProcessor."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_mistral = Mock(spec=MistralService)
        self.mock_memory = Mock(spec=MemoryService)
        self.processor = OrderProcessor(self.mock_mistral, self.mock_memory)
    
    def test_initialization(self):
        """Test processor initialization."""
        assert self.processor.mistral_service == self.mock_mistral
        assert self.processor.memory_service == self.mock_memory
    
    @pytest.mark.asyncio
    async def test_extract_order_data(self):
        """Test order data extraction."""
        # Mock the memory service
        self.mock_memory.get_relevant_context.return_value = "test context"
        
        # Mock the mistral service
        mock_response = Mock()
        mock_response.text = '{"customer_name": "Test", "order_lines": [{"stockcode": "ABC123", "quantity": 1}]}'
        self.mock_mistral.generate_content.return_value = mock_response
        
        result = await self.processor.extract_order_data("test content")
        
        assert result is not None
        assert "customer_name" in result
    
    @pytest.mark.asyncio
    async def test_validate_extracted_order(self):
        """Test order validation."""
        valid_order = {
            "customer_details": {"debtor_id": 12345, "customer_order_number": "PO-123"},
            "order_lines": [{"stockcode": "ABC123", "orderquantity": 1.0}]
        }
        
        result = await self.processor.validate_extracted_order(valid_order)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_invalid_order(self):
        """Test invalid order validation."""
        invalid_order = {
            "customer_details": {"debtor_id": 12345},
            "order_lines": []  # Empty lines should fail
        }
        
        with pytest.raises(ValidationException):
            await self.processor.validate_extracted_order(invalid_order)