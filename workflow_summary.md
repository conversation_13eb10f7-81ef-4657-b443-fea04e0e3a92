# 🔄 Enhanced MistralAI System - Workflow Summary

## 🚀 **Main Processing Workflows**

### **1. Email-to-Order Processing Flow**
```
📧 Gmail Email
    ↓
📄 PDF Text Extraction
    ↓
🎯 Centralized Prompt Manager
    ├── 📋 Business Rules (templates/rules.txt)
    ├── 🔧 Base Prompts (System Identity, JSON Enforcement)
    ├── 📦 Order Prompts (Customer Rules, Shipping Logic)
    └── ⚙️ System Prompts (Error Recovery, Fallbacks)
    ↓
🧠 Enhanced MistralAI Service
    ├── 🔒 JSON Mode Enforcement
    ├── 🔄 Network Retry Logic (3x attempts)
    ├── 🛠️ Response Cleaning (<think> tag removal)
    └── 🎯 Business Rule Application
    ↓
📦 Order Processor
    ├── ✅ JSON Validation
    ├── 🎯 Customer-Specific Rules
    │   ├── Gateway → CUSTOMERS CARRIER
    │   ├── Brady → BEST WAY (drop-ship)
    │   ├── RSEA → DIRECT FREIGHT EXPRESS
    │   └── Woolworths → PO extraction
    ├── 📋 Data Validation
    └── 🔍 Debtor ID Handling
    ↓
💾 Supabase Database Storage
    ├── 📧 Email Records
    ├── 📦 Order Data
    ├── 🏷️ Customer Information
    └── 📊 Processing Logs
    ↓
💼 MYOB Payload Generation
    ↓
🏷️ Gmail Labeling (Processed/Review/Failed)
    ↓
📬 Notification Email
```

### **2. Email Summary & Analysis Flow**
```
📧 Email Content
    ↓
🎯 Centralized Prompt Manager
    ├── 📝 Summary Prompts (Intent Recognition)
    ├── 🔧 Base Prompts (Company Context)
    └── 📋 Business Rules Integration
    ↓
🧠 Enhanced MistralAI Service
    ├── 🔒 JSON Mode Enforcement
    ├── 🎯 Intent Classification
    │   ├── Quote Request
    │   ├── Price Inquiry
    │   ├── Stock Check
    │   ├── Purchase Order
    │   └── General Inquiry
    └── 📊 Urgency Assessment
    ↓
📝 Summary Processor
    ├── ✅ JSON Validation
    ├── 📋 Summary Generation
    ├── 🎯 Action Classification
    └── 📅 Timeline Extraction
    ↓
📄 Markdown Summary Generation
    ├── 📧 Email Metadata
    ├── 📦 Order Information (if present)
    ├── 🎯 Key Details
    └── 📋 Action Items
    ↓
💾 File System Storage
    └── 📁 markdown/summary_files.md
```

### **3. Error Handling & Recovery Flow**
```
🤖 LLM Request
    ↓
🌐 Network Check
    ├── ❌ DNS Failure → 🔄 DNS Retry (5x)
    ├── ❌ Connection Error → 🔄 Network Retry (3x)
    └── ❌ Timeout → 🔄 Exponential Backoff
    ↓
📝 Response Validation
    ├── ❌ Invalid JSON → 🧹 Aggressive Cleaning
    │   ├── Remove <think> tags
    │   ├── Extract JSON objects
    │   └── Pattern matching
    ├── ❌ No JSON Found → 🔄 Fallback Prompt
    └── ❌ Parsing Failed → 🛠️ Error Recovery
    ↓
🔄 Circuit Breaker Logic
    ├── ✅ Success → Reset Counter
    ├── ⚠️ Failure Count < 5 → Continue
    └── ❌ Failure Count ≥ 5 → Circuit Open
    ↓
📊 Result Processing
    ├── ✅ Success → Continue Processing
    ├── ⚠️ Partial Success → Flag for Review
    └── ❌ Complete Failure → Manual Review Queue
```

### **4. Agent Scaling & Integration Flow**
```
🆕 New Agent Development
    ↓
🎯 Prompt Manager Access
    ├── 📋 Automatic Business Rules
    ├── 🔧 Reusable Base Components
    ├── 📦 Specialized Prompt Types
    └── ⚙️ System-Level Prompts
    ↓
🤖 Agent Implementation
    ├── from llm_service.prompts.prompt_manager import prompt_manager
    ├── prompt = prompt_manager.get_[prompt_type]_prompt(content)
    └── response = await mistral_service.generate_content(prompt)
    ↓
✅ Instant Integration
    ├── 🎯 Business Rule Compliance
    ├── 🔒 JSON Mode Enforcement
    ├── 🔄 Network Resilience
    └── 🛠️ Error Recovery
    ↓
📈 Production Ready
```

## 🎯 **Key Decision Points**

### **Customer-Specific Routing**
```
📧 Email Analysis
    ↓
🔍 Customer Identification
    ├── Gateway Packaging → 🚚 CUSTOMERS CARRIER
    ├── Brady Corporation → 📦 BEST WAY (drop-ship)
    ├── RSEA → 🚛 DIRECT FREIGHT EXPRESS
    ├── Safety Xpress → 🚚 DELTA
    ├── Endeavour Group → 🚚 CAPITAL
    ├── Sitecraft → 📧 EMAIL WHEN READY
    └── Unknown → 🔄 Default Rules
```

### **Email Intent Classification**
```
📧 Email Content Analysis
    ↓
🎯 Keyword Detection
    ├── "quote" → 💰 Quote Request
    ├── "price" → 💲 Price Inquiry
    ├── "stock" → 📦 Stock Check
    ├── "purchase order" → 📋 Purchase Order
    └── Other → 📝 General Inquiry
    ↓
⚡ Urgency Assessment
    ├── "urgent" → 🔴 High Priority
    ├── "ASAP" → 🔴 High Priority
    ├── "immediate" → 🔴 Critical
    └── Other → 🟡 Normal Priority
```

### **Data Quality Validation**
```
📦 Extracted Order Data
    ↓
🔍 Validation Checks
    ├── Debtor ID → null if unknown (not 0)
    ├── Stock Codes → Remove whitespace
    ├── Quantities → Positive numbers only
    ├── Shipping Method → Apply customer rules
    └── PO Numbers → Extract from patterns
    ↓
✅ Quality Assurance
    ├── ✅ Valid → Continue Processing
    ├── ⚠️ Warnings → Flag for Review
    └── ❌ Invalid → Manual Review Queue
```

## 📊 **Performance Monitoring Points**

### **System Health Checks**
- 🧠 **MistralAI Service**: Response time, success rate, JSON compliance
- 💾 **Database Connections**: Supabase health, query performance
- 🌐 **Network Resilience**: Retry success rate, DNS resolution
- 🎯 **Prompt System**: Rule loading, component availability
- 📧 **Email Processing**: Throughput, accuracy, error rates

### **Business Rule Compliance**
- 🎯 **Customer Rules**: Correct shipping method application
- 📦 **SKU Mapping**: Accurate code transformations
- 💰 **Pricing Logic**: Freight handling, special instructions
- 📋 **Order Validation**: Complete data extraction, quality checks

This workflow summary provides a clear view of how the enhanced MistralAI system processes emails, handles errors, applies business rules, and scales to support new agents while maintaining consistency and reliability.