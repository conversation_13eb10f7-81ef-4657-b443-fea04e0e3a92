"""
Test script to verify the refactoring works correctly.
"""
import asyncio
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly after refactoring."""
    print("Testing imports...")
    
    try:
        # Test CLI module
        from cli import parse_arguments, setup_logging, main
        print("✅ CLI module imports successful")
        
        # Test reporting module
        from reporting import ReportingService
        print("✅ Reporting module imports successful")
        
        # Test refactored main processor
        from main_processor import EmailOrderProcessor
        print("✅ Main processor module imports successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_cli_argument_parsing():
    """Test that CLI argument parsing works."""
    print("\nTesting CLI argument parsing...")
    
    try:
        from cli import parse_arguments
        
        # Test with minimal arguments
        import sys
        original_argv = sys.argv
        sys.argv = ['cli.py', '--once', '--dry-run']
        
        args = parse_arguments()
        
        assert args.once == True
        assert args.dry_run == True
        assert args.max_emails == 5  # default value
        
        sys.argv = original_argv
        print("✅ CLI argument parsing works correctly")
        return True
    except Exception as e:
        print(f"❌ CLI argument parsing error: {e}")
        return False

def test_reporting_service():
    """Test that reporting service initializes correctly."""
    print("\nTesting reporting service...")
    
    try:
        from reporting import ReportingService
        
        # This should work even without Gmail service if we handle it gracefully
        reporting_service = ReportingService()
        print("✅ Reporting service initializes correctly")
        return True
    except Exception as e:
        print(f"❌ Reporting service error: {e}")
        return False

def test_processor_initialization():
    """Test that the processor can be initialized."""
    print("\nTesting processor initialization...")
    
    try:
        from main_processor import EmailOrderProcessor
        
        # This might fail due to missing credentials, but we can catch that
        try:
            processor = EmailOrderProcessor()
            print("✅ Processor initializes correctly")
            return True
        except Exception as init_error:
            if "credentials" in str(init_error).lower() or "auth" in str(init_error).lower():
                print("⚠️ Processor initialization skipped (missing credentials - expected)")
                return True
            else:
                raise init_error
    except Exception as e:
        print(f"❌ Processor initialization error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Refactored Email Order Processor")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_cli_argument_parsing,
        test_reporting_service,
        test_processor_initialization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Refactoring appears successful.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)