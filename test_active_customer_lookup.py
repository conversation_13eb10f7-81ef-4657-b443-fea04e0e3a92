#!/usr/bin/env python3
"""
Test script to verify active customer lookup functionality after uploading new data.
"""
import logging
from supabase_database_service import SupabaseService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_customer_lookup():
    """Test the customer lookup functionality with active customers."""
    
    db = SupabaseService()
    
    print("Testing Active Customer Lookup")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        "Woolworths",
        "WOOLWORTHS", 
        "woolworths",
        "Woolworths Group",
        "ALH GROUP",
        "Coles",
        "IGA",
        "Unknown Customer"
    ]
    
    for test_name in test_cases:
        print(f"\nTesting: '{test_name}'")
        try:
            customer = db.find_customer_by_name(test_name)
            if customer:
                print(f"  ✅ Found: ID {customer['debtor_id']} - {customer['customer_name']}")
            else:
                print(f"  ❌ Not found")
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    # Show total customer count
    print(f"\n" + "=" * 50)
    try:
        all_customers = db.get_all_customers()
        print(f"Total active customers in database: {len(all_customers)}")
        
        if len(all_customers) > 0:
            print(f"Sample customers (first 5):")
            for i, customer in enumerate(all_customers[:5]):
                print(f"  {i+1}. ID: {customer['debtor_id']} - {customer['customer_name']}")
    except Exception as e:
        print(f"Error getting customer count: {e}")

if __name__ == "__main__":
    test_customer_lookup()