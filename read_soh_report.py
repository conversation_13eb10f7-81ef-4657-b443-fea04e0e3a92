#!/usr/bin/env python3
"""
Read and analyze the SOHOrderListing.xlsx file to extract active customers.
"""
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def read_soh_order_listing(file_path: str) -> Optional[pd.DataFrame]:
    """Read the SOHOrderListing Excel file."""
    try:
        logger.info(f"Reading SOHOrderListing file: {file_path}")
        
        # Try reading the Excel file
        df = pd.read_excel(file_path)
        logger.info(f"Successfully read Excel file with {len(df)} rows and {len(df.columns)} columns")
        
        # Display basic info about the file
        logger.info(f"Columns: {list(df.columns)}")
        logger.info(f"First few rows:")
        print(df.head())
        
        return df
        
    except Exception as e:
        logger.error(f"Error reading Excel file: {e}")
        return None

def analyze_soh_data(df: pd.DataFrame, months_back: int = 24) -> Dict[str, Any]:
    """Analyze the SOH data to extract active customers."""
    try:
        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=months_back * 30)
        logger.info(f"Looking for orders after: {cutoff_date.strftime('%Y-%m-%d')}")
        
        # Look for date columns (common names)
        date_columns = []
        for col in df.columns:
            if any(date_word in col.lower() for date_word in ['date', 'created', 'order', 'invoice', 'due']):
                date_columns.append(col)
        
        logger.info(f"Potential date columns: {date_columns}")
        
        # Look for customer/debtor columns
        customer_columns = []
        for col in df.columns:
            if any(cust_word in col.lower() for cust_word in ['customer', 'debtor', 'account', 'client', 'name']):
                customer_columns.append(col)
        
        logger.info(f"Potential customer columns: {customer_columns}")
        
        # Look for ID columns
        id_columns = []
        for col in df.columns:
            if any(id_word in col.lower() for id_word in ['id', 'number', 'code']):
                id_columns.append(col)
        
        logger.info(f"Potential ID columns: {id_columns}")
        
        # Show sample data for analysis
        logger.info("Sample data from first 5 rows:")
        for i in range(min(5, len(df))):
            logger.info(f"Row {i}: {dict(df.iloc[i])}")
        
        # Try to identify active customers
        active_customers = set()
        
        # If we can identify customer ID and date columns, filter by date
        if date_columns and customer_columns:
            for date_col in date_columns:
                try:
                    # Convert to datetime
                    df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
                    
                    # Filter recent orders
                    recent_orders = df[df[date_col] >= cutoff_date]
                    
                    if len(recent_orders) > 0:
                        logger.info(f"Found {len(recent_orders)} recent orders using date column: {date_col}")
                        
                        # Extract customer IDs
                        for cust_col in customer_columns:
                            if cust_col in recent_orders.columns:
                                customers = recent_orders[cust_col].dropna().unique()
                                active_customers.update(customers)
                                logger.info(f"Found {len(customers)} unique customers from column: {cust_col}")
                        
                        break  # Use the first working date column
                        
                except Exception as e:
                    logger.warning(f"Error processing date column {date_col}: {e}")
                    continue
        
        return {
            'total_rows': len(df),
            'columns': list(df.columns),
            'date_columns': date_columns,
            'customer_columns': customer_columns,
            'id_columns': id_columns,
            'active_customers': list(active_customers),
            'active_customer_count': len(active_customers)
        }
        
    except Exception as e:
        logger.error(f"Error analyzing SOH data: {e}")
        return {}

def main():
    """Main function to read and analyze the SOH report."""
    file_path = r"C:\Users\<USER>\Desktop\SOHOrderListing.xlsx"
    
    # Read the Excel file
    df = read_soh_order_listing(file_path)
    if df is None:
        return
    
    # Analyze the data
    analysis = analyze_soh_data(df)
    
    print("\n" + "="*60)
    print("SOH ORDER LISTING ANALYSIS")
    print("="*60)
    print(f"Total rows: {analysis.get('total_rows', 0)}")
    print(f"Total columns: {len(analysis.get('columns', []))}")
    print(f"Date columns found: {analysis.get('date_columns', [])}")
    print(f"Customer columns found: {analysis.get('customer_columns', [])}")
    print(f"ID columns found: {analysis.get('id_columns', [])}")
    print(f"Active customers found: {analysis.get('active_customer_count', 0)}")
    
    if analysis.get('active_customers'):
        print("\nActive customers (first 10):")
        for i, customer in enumerate(analysis['active_customers'][:10]):
            print(f"  {i+1}. {customer}")
        
        if len(analysis['active_customers']) > 10:
            print(f"  ... and {len(analysis['active_customers']) - 10} more")

if __name__ == "__main__":
    main()