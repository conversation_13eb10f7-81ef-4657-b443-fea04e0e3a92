"""Base classes and interfaces for LLM service."""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
from pydantic import BaseModel


class BaseService(ABC):
    """Base service class providing common functionality."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the service is healthy."""
        pass


class BaseProcessor(ABC):
    """Base processor for handling specific tasks."""
    
    @abstractmethod
    async def process(self, input_data: Any) -> Any:
        """Process the input data."""
        pass


class BaseValidator(ABC):
    """Base validator for data validation."""
    
    @abstractmethod
    def validate(self, data: Any) -> bool:
        """Validate the provided data."""
        pass
    
    @abstractmethod
    def get_validation_errors(self) -> List[str]:
        """Get list of validation errors."""
        pass