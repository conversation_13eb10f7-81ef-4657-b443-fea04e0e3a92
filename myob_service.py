"""
MYOB API service for creating sales orders.
"""
import json
import logging
import requests
from datetime import datetime
from typing import Dict, Any, Optional

from config import config

logger = logging.getLogger(__name__)

class MyobService:
    """Service for MYOB EXO API operations."""
    
    def __init__(self):
        self.base_url = config.MYOB_BASE_URL
        self.headers = config.MYOB_HEADERS
        logger.info(f"Initialized MYOB service with base URL: {self.base_url}")
    def validate_order(self, minimal_order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Validate order with MYOB using minimal required fields.
        Returns fully populated order data ready for creation.
        """
        logger.info("Validating order with MYOB API")

        # Create minimal validation payload
        validation_payload = {
            "debtorid": minimal_order_data["debtorid"],
            "status": minimal_order_data["status"],
            "lines": []
        }

        # Add minimal line data (just stockcode and quantity)
        for line in minimal_order_data.get("lines", []):
            validation_payload["lines"].append({
                "stockcode": line["stockcode"],
                "orderquantity": line.get("orderquantity", 1.0)
            })

        # Include any other critical fields if present
        optional_fields = ["customerordernumber", "deliveryaddress", "extrafields"]
        for field in optional_fields:
            if field in minimal_order_data:
                validation_payload[field] = minimal_order_data[field]

        logger.debug(f"Validation payload: {json.dumps(validation_payload, indent=2)}")

        try:
            response = requests.post(
                f"{self.base_url}/salesorder/validate",
                json=validation_payload,
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()

            validation_result = response.json()
            logger.info("Order validation successful")

            return validation_result # This return should be inside the try block

        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB validation HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"MYOB validation error: {e}")
            raise
    
    def create_order(self, order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create order using validated payload from MYOB."""
        logger.info("Creating order in MYOB")
        
        # Use the validated payload as-is (MYOB returns exactly what we need to POST)
        logger.debug(f"Order payload: {json.dumps(order_data, indent=2)}")
        
        try:
            response = requests.post(
                f"{self.base_url}/salesorder/",
                json=order_data,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 201:
                logger.info("Order created successfully in MYOB")
                return response.json()
            else:
                logger.error(f"MYOB order creation HTTP error: {response.status_code} {response.reason}")
                logger.error(f"Response: {response.text}")
                response.raise_for_status()
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB order creation HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"MYOB order creation error: {e}")
            raise
    def validate_and_create_order(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Validate and create sales order in MYOB."""
        try:
            # First validate the order
            validation_result = self.validate_order(payload)
            
            if not validation_result or 'order' not in validation_result:
                logger.error("Validation response missing 'order' object")
                return None
            
            validated_payload = validation_result['order']
            
            # Then create the order with validated payload
            created_order = self.create_order(validated_payload)
            
            return created_order
        except Exception as e:
            logger.error(f"Failed to validate and create MYOB order: {e}")
            return None
    
    def create_sales_order(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create sales order in MYOB (alias for validate_and_create_order)."""
        return self.validate_and_create_order(payload)
    
    def get_all_debtors(self) -> Optional[list]:
        """Get all debtors from MYOB."""
        logger.info("Fetching all debtors from MYOB API")
        
        try:
            response = requests.get(
                f"{self.base_url}/debtor",
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()
            
            debtors = response.json()
            logger.info(f"Retrieved {len(debtors)} debtors from MYOB")
            return debtors
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB get debtors HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"MYOB get debtors error: {e}")
            return None
    
    def get_debtor_report(self, months_back: int = 24) -> Optional[list]:
        """Get debtor report from MYOB for the specified time period."""
        logger.info(f"Fetching debtor report from MYOB API (last {months_back} months)")
        
        try:
            from datetime import datetime, timedelta
            
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=months_back * 30)
            
            # Build query parameters for the report
            params = {
                'fromdate': start_date.strftime('%Y-%m-%d'),
                'todate': end_date.strftime('%Y-%m-%d')
            }
            
            response = requests.get(
                f"{self.base_url}/debtordebtorreport",
                headers=self.headers,
                params=params,
                timeout=60  # Longer timeout for potentially large dataset
            )
            response.raise_for_status()
            
            report_data = response.json()
            logger.info(f"Retrieved debtor report with {len(report_data)} entries from MYOB")
            return report_data
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB get debtor report HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"MYOB get debtor report error: {e}")
            return None
    
    def get_debtor_transactions(self, debtor_id: Optional[int] = None, months_back: int = 24) -> Optional[list]:
        """Get debtor transactions from MYOB, optionally filtered by debtor_id and date range."""
        logger.info(f"Fetching debtor transactions from MYOB API (last {months_back} months)")
        
        try:
            from datetime import datetime, timedelta
            
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=months_back * 30)
            
            # Build query parameters
            params = {
                'fromdate': start_date.strftime('%Y-%m-%d'),
                'todate': end_date.strftime('%Y-%m-%d')
            }
            
            if debtor_id:
                params['debtorid'] = debtor_id
            
            response = requests.get(
                f"{self.base_url}/debtordebtortrans",
                headers=self.headers,
                params=params,
                timeout=60  # Longer timeout for potentially large dataset
            )
            response.raise_for_status()
            
            transactions = response.json()
            logger.info(f"Retrieved {len(transactions)} debtor transactions from MYOB")
            return transactions
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB get debtor transactions HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"MYOB get debtor transactions error: {e}")
            return None
    
    def get_active_debtors(self, months_back: int = 24) -> Optional[list]:
        """Get active debtors using the most efficient method available."""
        logger.info(f"Getting active debtors (last {months_back} months)")
        
        try:
            # First try using the SOH report file if it exists (most accurate)
            soh_file_path = r"C:\Users\<USER>\Desktop\SOHOrderListing.xlsx"
            import os
            if os.path.exists(soh_file_path):
                logger.info("SOH report file found, using it for active debtors...")
                active_debtors = self.get_active_debtors_from_soh_report(soh_file_path, months_back)
                if active_debtors and len(active_debtors) > 0:
                    logger.info(f"Successfully retrieved {len(active_debtors)} active debtors from SOH report")
                    return active_debtors
                else:
                    logger.warning("SOH report returned no active debtors, falling back to API methods")
            else:
                logger.info("SOH report file not found, using API methods")
            
            # Fallback to MYOB API date filtering approach
            logger.info("Trying MYOB API date filtering approach...")
            active_debtors = self.get_active_debtors_with_filter(months_back)
            if active_debtors and len(active_debtors) > 0:
                logger.info(f"Successfully retrieved {len(active_debtors)} active debtors using date filtering")
                return active_debtors
            
            # Final fallback to sales order approach if filtering fails or returns no results
            logger.info("Date filtering failed or returned no results, falling back to sales order approach")
            return self.get_active_debtors_from_sales_orders(months_back)
            
        except Exception as e:
            logger.error(f"Error getting active debtors: {e}")
            return None
    
    def get_active_debtors_from_sales_orders(self, months_back: int = 24) -> Optional[list]:
        """Get active debtors by analyzing recent sales orders (fallback method)."""
        logger.info(f"Getting active debtors from sales orders (last {months_back} months)")
        
        try:
            from datetime import datetime, timedelta
            
            # Calculate cutoff date
            cutoff_date = datetime.now() - timedelta(days=months_back * 30)
            logger.info(f"Looking for sales orders after: {cutoff_date.strftime('%Y-%m-%d')}")
            
            # Get all sales orders (we know this endpoint works)
            logger.info("Fetching all sales orders to identify active debtors...")
            all_sales_orders = self.get_all_sales_orders_paginated()
            
            if not all_sales_orders:
                logger.warning("No sales orders found")
                return []
            
            # Filter sales orders by date and extract active debtor IDs
            active_debtor_ids = set()
            recent_orders_count = 0
            
            for order in all_sales_orders:
                # Check if order is recent enough
                order_date_str = order.get('createdate') or order.get('orderdate') or order.get('finalisationdate')
                if order_date_str:
                    try:
                        # Parse the date (handle various formats)
                        if 'T' in order_date_str:
                            order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00'))
                        else:
                            order_date = datetime.strptime(order_date_str, '%Y-%m-%d')
                        
                        if order_date >= cutoff_date:
                            debtor_id = order.get('debtorid')
                            if debtor_id:
                                active_debtor_ids.add(debtor_id)
                                recent_orders_count += 1
                    except (ValueError, TypeError) as e:
                        # If date parsing fails, include the order (better to be inclusive)
                        logger.debug(f"Date parsing failed for order {order.get('id', 'unknown')}: {e}")
                        debtor_id = order.get('debtorid')
                        if debtor_id:
                            active_debtor_ids.add(debtor_id)
                            recent_orders_count += 1
            
            logger.info(f"Found {recent_orders_count} recent orders from {len(active_debtor_ids)} unique active debtors")
            
            # Get full debtor details for active debtors
            all_debtors = self.get_all_debtors_paginated()
            if not all_debtors:
                logger.error("Failed to get debtor details")
                return None
            
            # Filter to only active debtors (note: debtors use 'id' field, not 'debtorid')
            active_debtors = [
                debtor for debtor in all_debtors 
                if debtor.get('id') in active_debtor_ids
            ]
            
            logger.info(f"Retrieved details for {len(active_debtors)} active debtors")
            return active_debtors
            
        except Exception as e:
            logger.error(f"Error getting active debtors from sales orders: {e}")
            return None
    
    def get_soh_order_listing_report(self, start_date: datetime, end_date: datetime) -> Optional[list]:
        """Get SOHOrderListing report for processed sales orders in the specified date range."""
        logger.info(f"Fetching SOHOrderListing report from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        try:
            # First, try to run the report
            logger.info("Running SOHOrderListing report...")
            run_params = {
                'reportname': 'SOHOrderListing.clr',
                'fromdate': start_date.strftime('%Y-%m-%d'),
                'todate': end_date.strftime('%Y-%m-%d')
            }
            
            # Try the runreport endpoint
            run_response = requests.post(
                f"{self.base_url}/report/runreport",
                json=run_params,
                headers=self.headers,
                timeout=120
            )
            
            if run_response.status_code == 200:
                logger.info("Report run successful, fetching results...")
                # Try to fetch the report results
                fetch_response = requests.get(
                    f"{self.base_url}/report/fetchreport",
                    headers=self.headers,
                    params={'reportname': 'SOHOrderListing.clr'},
                    timeout=120
                )
                
                if fetch_response.status_code == 200:
                    report_data = fetch_response.json()
                    logger.info(f"Retrieved SOHOrderListing report with {len(report_data) if isinstance(report_data, list) else 'data'}")
                    return report_data if isinstance(report_data, list) else []
                else:
                    logger.warning(f"Fetch report failed: {fetch_response.status_code} - {fetch_response.text}")
            else:
                logger.warning(f"Run report failed: {run_response.status_code} - {run_response.text}")
            
            # Fallback: try direct report endpoint with different parameter formats
            logger.info("Trying direct report endpoint as fallback...")
            
            # Try different parameter combinations
            param_variations = [
                {
                    'report': 'SOHOrderListing.clr',
                    'fromdate': start_date.strftime('%Y-%m-%d'),
                    'todate': end_date.strftime('%Y-%m-%d')
                },
                {
                    'reportname': 'SOHOrderListing.clr',
                    'startdate': start_date.strftime('%Y-%m-%d'),
                    'enddate': end_date.strftime('%Y-%m-%d')
                },
                {
                    'name': 'SOHOrderListing.clr',
                    'from': start_date.strftime('%Y-%m-%d'),
                    'to': end_date.strftime('%Y-%m-%d')
                }
            ]
            
            for i, params in enumerate(param_variations):
                logger.info(f"Trying parameter variation {i+1}: {params}")
                response = requests.get(
                    f"{self.base_url}/report",
                    headers=self.headers,
                    params=params,
                    timeout=120
                )
                
                if response.status_code == 200:
                    report_data = response.json()
                    if report_data and (isinstance(report_data, list) and len(report_data) > 0):
                        logger.info(f"Success with variation {i+1}! Retrieved {len(report_data)} records")
                        return report_data
                    else:
                        logger.info(f"Variation {i+1} returned empty data")
                else:
                    logger.warning(f"Variation {i+1} failed: {response.status_code}")
            
            logger.warning("All report parameter variations failed or returned empty data")
            return []
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB SOHOrderListing report HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"MYOB SOHOrderListing report error: {e}")
            return None
    
    def get_all_sales_orders(self) -> Optional[list]:
        """Get all sales orders from MYOB."""
        logger.info("Fetching all sales orders from MYOB API")
        
        try:
            response = requests.get(
                f"{self.base_url}/salesorder",
                headers=self.headers,
                timeout=60  # Longer timeout for potentially large dataset
            )
            response.raise_for_status()
            
            orders = response.json()
            logger.info(f"Retrieved {len(orders)} sales orders from MYOB")
            return orders
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB get sales orders HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"MYOB get sales orders error: {e}")
            return None
    
    def get_all_sales_orders_paginated(self) -> Optional[list]:
        """Get all sales orders from MYOB with pagination support."""
        logger.info("Fetching all sales orders from MYOB API (with pagination)")
        
        all_orders = []
        page = 1
        page_size = 100  # Reasonable page size
        
        try:
            while True:
                logger.debug(f"Fetching sales orders page {page}")
                response = requests.get(
                    f"{self.base_url}/salesorder",
                    headers=self.headers,
                    params={'page': page, 'pagesize': page_size},
                    timeout=60
                )
                response.raise_for_status()
                
                orders = response.json()
                if not orders or len(orders) == 0:
                    break
                
                all_orders.extend(orders)
                logger.debug(f"Retrieved {len(orders)} orders from page {page}")
                
                # If we got fewer than page_size, we're done
                if len(orders) < page_size:
                    break
                
                page += 1
                
                # Safety check to prevent infinite loops
                if page > 1000:  # Max 100k orders
                    logger.warning("Reached maximum page limit, stopping pagination")
                    break
            
            logger.info(f"Retrieved {len(all_orders)} total sales orders from MYOB")
            return all_orders
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB get sales orders HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            # Fallback to non-paginated approach
            logger.info("Falling back to non-paginated sales orders")
            return self.get_all_sales_orders()
        except Exception as e:
            logger.error(f"MYOB get sales orders error: {e}")
            return None
    
    def get_all_debtors_paginated(self) -> Optional[list]:
        """Get all debtors from MYOB with pagination support."""
        logger.info("Fetching all debtors from MYOB API (with pagination)")
        
        all_debtors = []
        page = 1
        page_size = 100  # Reasonable page size
        
        try:
            while True:
                logger.debug(f"Fetching debtors page {page}")
                response = requests.get(
                    f"{self.base_url}/debtor",
                    headers=self.headers,
                    params={'page': page, 'pagesize': page_size},
                    timeout=60
                )
                response.raise_for_status()
                
                debtors = response.json()
                if not debtors or len(debtors) == 0:
                    break
                
                all_debtors.extend(debtors)
                logger.debug(f"Retrieved {len(debtors)} debtors from page {page}")
                
                # If we got fewer than page_size, we're done
                if len(debtors) < page_size:
                    break
                
                page += 1
                
                # Safety check to prevent infinite loops
                if page > 1000:  # Max 100k debtors
                    logger.warning("Reached maximum page limit, stopping pagination")
                    break
            
            logger.info(f"Retrieved {len(all_debtors)} total debtors from MYOB")
            return all_debtors
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB get debtors HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            # Fallback to non-paginated approach
            logger.info("Falling back to non-paginated debtors")
            return self.get_all_debtors()
        except Exception as e:
            logger.error(f"MYOB get debtors error: {e}")
            return None
    
    def get_endpoint_schema(self, endpoint: str, schema_type: str = "GET") -> Optional[dict]:
        """Get schema information for a MYOB API endpoint."""
        logger.info(f"Getting {schema_type} schema for endpoint: {endpoint}")
        
        try:
            response = requests.get(
                f"{self.base_url}/{endpoint}",
                headers=self.headers,
                params={'$schema': schema_type},
                timeout=30
            )
            response.raise_for_status()
            
            schema = response.json()
            logger.info(f"Retrieved {schema_type} schema for {endpoint}")
            return schema
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB schema request HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"MYOB schema request error: {e}")
            return None
    
    def get_active_debtors_with_filter(self, months_back: int = 24) -> Optional[list]:
        """Get active debtors using MYOB API filtering by last_updated date with pagination."""
        logger.info(f"Getting active debtors using date filter (last {months_back} months)")
        
        try:
            from datetime import datetime, timedelta
            
            # Calculate cutoff date
            cutoff_date = datetime.now() - timedelta(days=months_back * 30)
            cutoff_str = cutoff_date.strftime('%Y-%m-%d')
            
            # Use MYOB API filtering to get recently updated debtors with pagination
            logger.info(f"Filtering debtors updated after: {cutoff_str}")
            
            all_active_debtors = []
            page = 1
            page_size = 100  # Maximum page size
            
            while True:
                logger.debug(f"Fetching filtered debtors page {page}")
                response = requests.get(
                    f"{self.base_url}/debtor",
                    headers=self.headers,
                    params={
                        '$filter': f"a.last_updated ge '{cutoff_str}'",
                        'page': page,
                        'pagesize': page_size
                    },
                    timeout=60
                )
                response.raise_for_status()
                
                debtors = response.json()
                if not debtors or len(debtors) == 0:
                    break
                
                all_active_debtors.extend(debtors)
                logger.debug(f"Retrieved {len(debtors)} debtors from page {page}")
                
                # If we got fewer than page_size, we're done
                if len(debtors) < page_size:
                    break
                
                page += 1
                
                # Safety check to prevent infinite loops
                if page > 1000:  # Max 100k debtors
                    logger.warning("Reached maximum page limit, stopping pagination")
                    break
            
            logger.info(f"Retrieved {len(all_active_debtors)} active debtors using date filter")
            return all_active_debtors
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB filtered debtors HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"MYOB filtered debtors error: {e}")
            return None
    
    def get_active_debtors_from_soh_report(self, soh_file_path: str, months_back: int = 24) -> Optional[list]:
        """Get active debtors from SOHOrderListing Excel file."""
        logger.info(f"Getting active debtors from SOH report: {soh_file_path}")
        
        try:
            import pandas as pd
            from datetime import datetime, timedelta
            
            # Read the Excel file
            df = pd.read_excel(soh_file_path)
            logger.info(f"Read SOH report with {len(df)} orders")
            
            # Calculate cutoff date
            cutoff_date = datetime.now() - timedelta(days=months_back * 30)
            
            # Convert order date to datetime
            df['Ord Date'] = pd.to_datetime(df['Ord Date'], errors='coerce')
            
            # Filter recent orders
            recent_orders = df[df['Ord Date'] >= cutoff_date]
            logger.info(f"Found {len(recent_orders)} recent orders after {cutoff_date.strftime('%Y-%m-%d')}")
            
            # Extract unique debtor IDs from Account column
            active_debtor_ids = recent_orders['Account'].dropna().unique()
            # Convert to integers and remove any invalid values
            active_debtor_ids = [int(id) for id in active_debtor_ids if pd.notna(id) and id > 0]
            
            logger.info(f"Found {len(active_debtor_ids)} unique active debtor IDs from SOH report")
            
            # Get full debtor details for active debtors (use paginated approach)
            all_debtors = self.get_all_debtors_paginated()
            if not all_debtors:
                logger.error("Failed to get debtor details from MYOB API")
                return None
            
            # Filter to only active debtors
            active_debtors = [
                debtor for debtor in all_debtors 
                if debtor.get('id') in active_debtor_ids
            ]
            
            logger.info(f"Retrieved details for {len(active_debtors)} active debtors from MYOB API")
            return active_debtors
            
        except Exception as e:
            logger.error(f"Error getting active debtors from SOH report: {e}")
            return None
    
    def search_debtor(self, query: str) -> Optional[list]:
        """Search for debtors by name or other criteria."""
        logger.info(f"Searching debtors in MYOB for: '{query}'")
        
        try:
            response = requests.get(
                f"{self.base_url}/debtor/search",
                headers=self.headers,
                params={'q': query},
                timeout=30
            )
            response.raise_for_status()
            
            results = response.json()
            logger.info(f"Found {len(results)} debtor search results")
            return results
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB debtor search HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"MYOB debtor search error: {e}")
            return None
